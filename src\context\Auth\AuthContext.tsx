import { notify } from '@context/Notifier/NotifierContext';
import { useNetwork } from '@hooks/useNetwork';
import { router } from '@navigation/refs/navigation-ref';
import { userDb } from '@packages/db/functions';
import useStitch from '@packages/useStitch';
import { SessionUser, SessionUserResponse } from '@packages/useStitch/types';
import { ApiResponse } from '@packages/useStitch/types/common-api-types';
import React, { ReactNode, createContext, useContext, useEffect, useState } from 'react';

type AuthenticatedState = {
  isAuthenticated: true;
  session: SessionUserResponse;
  isLoading: boolean;
  isInitialLoading?: boolean;
  handleVerifyOtpSuccess?: (data: ApiResponse<SessionUserResponse>) => void;
  refetchUserInfo?: () => Promise<void>;
  autoLogin?: () => Promise<void>;
  handleLogout?: () => Promise<void>;
  isLoggingOut?: boolean;
  updateAccessToken?: (accessToken: string) => Promise<void>;
};

type UnauthenticatedState = {
  isAuthenticated: false;
  session: null;
  isLoading: boolean;
  isInitialLoading?: boolean;
  handleVerifyOtpSuccess?: (data: ApiResponse<SessionUserResponse>) => void;
  refetchUserInfo?: () => Promise<void>;
  autoLogin?: () => Promise<void>;
  handleLogout?: () => Promise<void>;
  isLoggingOut?: boolean;
  updateAccessToken?: (accessToken: string) => Promise<void>;
};

export type AuthStateType = AuthenticatedState | UnauthenticatedState;

const initialAuthValue: AuthStateType = {
  isAuthenticated: false,
  session: null,
  isLoading: false,
  isInitialLoading: false,
  handleVerifyOtpSuccess: () => {},
  refetchUserInfo: async () => {},
  handleLogout: async () => {},
  updateAccessToken: async (accessToken: string) => {},
  isLoggingOut: false,
};

const AuthContext = createContext<AuthStateType>(initialAuthValue);

export let memAccessToken: string | null = null;

export const setMemAccessToken = (newToken: string) => {
  memAccessToken = newToken;
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isOnline } = useNetwork();
  const { mutateAsync: fetchUserInfo } = useStitch('me');
  const { mutateAsync: logoutFromServer, isMutating: isLoggingOut } = useStitch('logout');
  const { mutateAsync } = useStitch('autoLogin', { disableDefaultNotifier: true });
  const [authState, setAuthState] = useState<AuthStateType>(initialAuthValue);

  const tryLocalDbLogin = async () => {
    try {
      const localUser = await userDb.getUser();
      console.log('Trying lcoal db login >>> ', localUser);
      if (localUser != null) {
        const {
          age,
          isSignupCompleted,
          id,
          avatar,
          bio,
          email,
          gender,
          location,
          playlistLink,
          idShowCurrentMoodOnProfile,
          totalFollowers,
          totalFollowing,
          totalHealCount,
          totalPostCount,
          username,
          accessToken,
          refreshToken,
        } = localUser;
        memAccessToken = accessToken;
        setAuthState({
          isLoading: false,
          isAuthenticated: true,
          session: {
            accessToken: accessToken,
            email: email,
            refreshToken: refreshToken,
            isSignupCompleted: isSignupCompleted,
            user: {
              age: age,
              avatar,
              profilePercentage: 80,
              bio,
              email,
              gender: gender as any,
              location,
              playlistLink,
              showCurrentMoodOnProfile: idShowCurrentMoodOnProfile,
              totalFollowers,
              totalFollowing,
              totalHealCount,
              totalPostCount,
              id,
              username,
            },
            username: localUser.username,
          },
        });
        return {
          status: true,
          user: localUser,
        };
      }

      return {
        status: false,
        user: null,
      };
    } catch (error) {
      console.log('Auto login error local', error);
      return {
        status: false,
        user: null,
      };
    }
  };

  const autoLogin = async () => {
    // First fecthes from local db -> then sets immendailtey the state to as per db if not null then in bg fectehs from server if this auth token get error then logs out and clear the user info.
    // also if any db error or intilazation error then also redirects to login and as it is working.
    try {
      const { user } = await tryLocalDbLogin();
      if (!user) return;
      // Dont call api if user is not present in local db.
      const data = await mutateAsync({
        refreshToken: user?.refreshToken ?? '',
      });

      setAuthState({
        isAuthenticated: true,
        isLoading: false,
        isInitialLoading: false,
        session: {
          accessToken: data.data.accessToken,
          refreshToken: data.data.refreshToken,
          email: data.data.email,
          isSignupCompleted: data.data.isSignupCompleted ?? false,
          user: data.data.user,
          username: data.data.username,
        },
      });

      const extras = {
        accessToken: data.data.accessToken,
        refreshToken: data.data.refreshToken,
        isAuthenticated: true,
        isSignupCompleted: data.data.isSignupCompleted ?? false,
      };

      memAccessToken = data.data.accessToken;
      userDb.setUser(extras, data?.data?.user ?? undefined);

      if (!data.data.isSignupCompleted) {
        router.navigate('GetStarted');
      }
    } catch (error) {
      if (isOnline) {
        await userDb.deleteAllUsers();
        await userDb.logout();
        setAuthState({
          isAuthenticated: false,
          isLoading: false,
          session: null,
        });
      } else {
        setAuthState({
          ...authState,
          isLoading: false,
        });
      }
    }
  };

  /**
   *
   * @param data
   */
  const handleVerifyOtpSuccess = async (data: ApiResponse<SessionUserResponse>) => {
    try {
      // Handling manual Signup and Login success
      // First we need to set the auth state to true  and upsert the data to local db.
      const extras = {
        accessToken: data.data.accessToken,
        refreshToken: data.data.refreshToken,
        isAuthenticated: true,
        isSignupCompleted: data.data.isSignupCompleted ?? false,
      };
      const user = data?.data?.user;
      memAccessToken = data.data.accessToken;

      await userDb.setUser(extras, user ?? undefined);
      await tryLocalDbLogin();
    } catch (error) {
      console.log('>>>Verify otp error >>> ', error);
    }
  };

  const handleLogout = async () => {
    if (!isOnline) return;

    try {
      await logoutFromServer({ refreshToken: authState.session?.refreshToken! });
      await userDb.logout();
      await userDb.deleteAllUsers();
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        session: null,
      });
      memAccessToken = null;
      notify.center('Logged out successfully.', 'info', 3000, 'center');
    } catch (error) {
      // notify.center('Something went wrong , unable to logout');
    }
  };

  useEffect(() => {
    autoLogin();
  }, []);

  const updateUserInfoOnly = (user: SessionUser) => {
    try {
      // first update the state upfront and then upsert to local db
      const updatedAuthState = {
        ...authState,
        isLoading: false,
        isInitialLoading: false,
        session: {
          ...authState.session,
          isSignupCompleted: true,
          user: {
            ...authState.session?.user,
            id: user.id,
            age: user.age ?? '',
            avatar: user.avatar ?? '',
            bio: user.bio ?? '',
            email: user.email,
            gender: user.gender ?? '',
            location: user.location ?? '',
            playlistLink: user.playlistLink ?? '',
            profilePercentage: user.profilePercentage ?? 0,
            showCurrentMoodOnProfile: user.showCurrentMoodOnProfile ?? false,
            totalFollowers: user.totalFollowers ?? 0,
            totalFollowing: user.totalFollowing ?? 0,
            totalHealCount: user.totalHealCount ?? 0,
            totalPostCount: user.totalPostCount ?? 0,
            username: user.username ?? '',
          },
        },
        isAuthenticated: true,
      } as AuthStateType;
      setAuthState(updatedAuthState);

      // Upsert to local db
      const session = authState.session;
      const extras = {
        accessToken: session!.accessToken,
        refreshToken: session!.refreshToken,
        isAuthenticated: true,
        isSignupCompleted: true,
      };
      if (session?.accessToken) {
        memAccessToken = session.accessToken;
      }

      userDb.setUser(extras, user);
    } catch (error) {
      console.log('Error >>', error);
    }
  };

  const refetchUserInfo = async () => {
    try {
      const res = await fetchUserInfo();
      if (res.data.username) {
        updateUserInfoOnly(res.data);
        notify.bottom('Userinfo updated successfully.');
      }
    } catch (error) {
      // console.log('Unable to update user info', error);
    }
  };

  const updateAccessToken = async (newAccessToken: string) => {
    try {
      const newState = {
        ...authState,
        session: {
          ...authState.session,
          accessToken: newAccessToken,
        },
      };
      const extras = {
        accessToken: newAccessToken,
        refreshToken: authState.session?.refreshToken ?? '',
        isAuthenticated: authState.isAuthenticated,
        isSignupCompleted: authState.session?.isSignupCompleted ?? false,
      };
      setAuthState(newState as AuthStateType);
      userDb.setUser(extras, authState.session!.user!);
    } catch (error) {
      console.log('Unable to update access token..');
    }
  };

  return (
    <AuthContext.Provider
      value={{
        ...authState,
        handleVerifyOtpSuccess,
        refetchUserInfo,
        autoLogin,
        handleLogout,
        isLoggingOut,
        updateAccessToken,
      }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthStateType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
