import { FF } from '@/types/layout-types';
import { AnimatedPressable, Text, View } from '@components/native';
import { useTheme } from '@context/index';
import { useBottomSheetModal } from '@gorhom/bottom-sheet';
import React, { forwardRef, isValidElement } from 'react';
import { Dimensions, Platform, FlatList as RNFlatList, FlatListProps as RNFlatListProps } from 'react-native';
import Animated, { useAnimatedStyle, withTiming } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StackBackButton } from '.';

interface FlatListProps<T> extends RNFlatListProps<T> {
  customHeaderComponent?: React.ReactNode;
  customFooterComponent?: React.ReactNode;
  title?: string | React.ReactNode;
  titleFF?: FF;
  /**
   * Default false.
   */
  hideHeader?: boolean;
}

const { width } = Dimensions.get('screen');
const HEADER_HEIGHT = 70;

export const FlatList = forwardRef<RNFlatList<any>, FlatListProps<any>>(
  <T,>(
    { title, contentContainerStyle, inverted, customHeaderComponent, hideHeader = false, customFooterComponent, titleFF = 'Montserrat-Medium', ...props }: FlatListProps<T>,
    ref: React.Ref<RNFlatList<T>>,
  ) => {
    const { dismissAll } = useBottomSheetModal();
    const { top } = useSafeAreaInsets();
    const { colors, sheetValue } = useTheme();

    // Animated style for the overlay
    const overlayStyle = useAnimatedStyle(() => ({
      opacity: withTiming(sheetValue.get() ? 0.2 : 0, { duration: 300 }),
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'black',
      zIndex: 5, // Below bottom sheet (zIndex > 10)
      pointerEvents: sheetValue.get() ? 'auto' : 'none',
    }));

    const titleheader = (
      <>
        {isValidElement(title) ? (
          title
        ) : (
          <Text ff={titleFF} fs="20" fw="600" color="neutral90" ml={10}>
            {title}
          </Text>
        )}
      </>
    );

    const shadowStyles = {
      ...Platform.select({
        ios: {
          shadowColor: '#000000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 6,
          zIndex: 10,
        },
        android: {
          elevation: 6,
          zIndex: 10,
        },
      }),
    };

    const HEADER_PADDING = hideHeader ? 0 : top + HEADER_HEIGHT + 5;

    return (
      <>
        <AnimatedPressable onPress={dismissAll} style={overlayStyle} />
        <RNFlatList
          {...props}
          ref={ref}
          contentContainerStyle={{
            backgroundColor: colors.background,
            paddingTop: inverted ? 0 : HEADER_PADDING,
            paddingBottom: inverted ? HEADER_PADDING : 0,
            ...(contentContainerStyle as any),
          }}
          inverted={inverted}
          style={{ backgroundColor: colors.background }}
          showsVerticalScrollIndicator={props.showsVerticalScrollIndicator ?? false}
          scrollEventThrottle={16}
        />
        {!hideHeader && (
          <View w={width} disableSizeMatter pos="absolute" h={top + HEADER_HEIGHT} bg="background" style={shadowStyles}>
            <View pos="relative" display="flex" fd="row" ai="center" px={20} top={top} disableSizeMatter h={HEADER_HEIGHT} w={width}>
              <StackBackButton withAbsolute={false} />
              <View flex={1} disableSizeMatter>
                {customHeaderComponent ? customHeaderComponent : titleheader}
              </View>
            </View>
            <AnimatedPressable onPress={dismissAll} style={overlayStyle} />
          </View>
        )}
        {customFooterComponent}
      </>
    );
  },
);

FlatList.displayName = 'FlatList';

export const AnimatedFlatList = Animated.createAnimatedComponent(FlatList);

AnimatedFlatList.displayName = 'AnimatedFlatList';
