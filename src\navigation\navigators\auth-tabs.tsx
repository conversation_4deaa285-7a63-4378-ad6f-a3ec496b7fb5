import BottomCurve from '@assets/svgs/bottom-curve.svg';
import ChatIcon from '@assets/svgs/chat-icon.svg';
import FeedIcon from '@assets/svgs/feed-icon.svg';
import Hamburger from '@assets/svgs/hamburger-icon.svg';
import JournalIcon from '@assets/svgs/journal-icon.svg';
import MoodIcon from '@assets/svgs/mood-icon.svg';
import ProfileIcon from '@assets/svgs/profile-icon.svg';
import { AnimatedPressable, Text, View } from '@components/native';
import { BounceTap } from '@components/shared/animated';
import { useAuth, useTheme } from '@context/index';
import { ConversationsScreen } from '@features/chat';
import { FeedScreen } from '@features/feed';
import { JournalScreen } from '@features/journal';
import { MoodScreen } from '@features/mood-tracker';
import { MyProfileScreen } from '@features/profile';
import { useBottomSheetModal } from '@gorhom/bottom-sheet';
import { TabScreenNames, router } from '@navigation/refs/navigation-ref';
import { BottomTabBarProps, BottomTabHeaderProps, createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import SCREENS from '@src/SCREENS';
import React, { memo } from 'react';
import { Pressable, useWindowDimensions } from 'react-native';
import Animated, { useAnimatedStyle, withTiming } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { CustomSvgProps } from 'types';

const Tab = createBottomTabNavigator();

const tabs = [
  {
    title: 'Chat',
    headerTitle: (title?: string) => 'Chat',
    icon: (props: CustomSvgProps) => <ChatIcon {...props} />,
    route: SCREENS.CONVERSATIONS,
  },
  {
    title: 'Feed',
    icon: (props: CustomSvgProps) => <FeedIcon {...props} />,
    headerTitle: (title?: string) => 'Feed',
    route: SCREENS.FEED,
  },
  {
    title: 'Journal',
    headerTitle: (title?: string) => 'Your Journals',
    icon: (props: CustomSvgProps) => <JournalIcon {...props} />,
    route: SCREENS.JOURNAL,
  },
  {
    title: 'Mood',
    headerTitle: (title?: string) => 'Mood Tracker',
    icon: (props: CustomSvgProps) => <MoodIcon {...props} />,
    route: SCREENS.MOOD_TRACKER,
  },
  {
    title: 'Profile',
    headerTitle: (title?: string) => title ?? 'Profile',
    icon: (props: CustomSvgProps) => <ProfileIcon {...props} />,
    route: SCREENS.PROFILE,
  },
] as const;

const DrawerHeaderComponent = memo(({ navigation: { getState } }: BottomTabHeaderProps) => {
  const state = getState();
  const { session } = useAuth();
  const { theme, colors, sheetValue } = useTheme();
  const { top } = useSafeAreaInsets();
  const { dismissAll } = useBottomSheetModal();

  // Animated style for the overlay
  const overlayStyle = useAnimatedStyle(() => ({
    opacity: withTiming(sheetValue.get() ? 0.2 : 0, { duration: 300 }),
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'black',
    zIndex: 5, // Below bottom sheet (zIndex > 10)
    pointerEvents: sheetValue.get() ? 'auto' : 'none',
  }));

  return (
    <View pt={top} mih={70} display="flex" fd="row" ai="center" gap={15} disableSizeMatter pr={20} py={10} style={{ backgroundColor: theme === 'dark' ? '#081012' : 'white' }}>
      <AnimatedPressable onPress={dismissAll} style={overlayStyle} />
      <BounceTap onPress={router.openDrawer} pl={20}>
        <Hamburger stroke={colors.neutral100} />
      </BounceTap>
      <Text fw="600" fs="20" ff="PlayfairDisplay-SemiBold" color="neutral100">
        {tabs[state.index].headerTitle(`@${session?.user?.username}`)}
      </Text>
    </View>
  );
});

const CustomAnimatedTabBar = memo(({ insets, state }: BottomTabBarProps) => {
  const { width } = useWindowDimensions();
  const { colors, isDarkMode, theme } = useTheme();

  return (
    <View disableSizeMatter display="flex" fd="row" jc="space-between" h={25 + insets.bottom} style={{ backgroundColor: theme === 'dark' ? '#081012' : 'white' }}>
      {tabs.map((tab, i) => {
        const active = state.index === i;

        const EACH_CONTAINER_WIDTH = width / tabs.length;

        const Icon = tab.icon;
        const fill = active ? (isDarkMode ? colors.purple200 : colors.purple800) : isDarkMode ? colors.warning30 : colors.purple500;

        return (
          <Pressable key={i} onPress={() => router.tabNavigate(tab.route as TabScreenNames)}>
            {!active ? (
              <View
                pos="absolute"
                top={-32}
                style={{
                  borderTopColor: colors.orange,
                  borderTopWidth: 2,
                  borderRightColor: colors.orange,
                  backgroundColor: theme === 'dark' ? '#081012' : 'white',
                }}
                disableSizeMatter
                w={EACH_CONTAINER_WIDTH + 2}
                bg="background"
                left={-1}
                flexCenterRow
                h={40}>
                <Icon fill={fill} />
              </View>
            ) : (
              <View top={-46} style={{ transform: [{ scaleX: 1.0001 }] }} pos="absolute" h={55} disableSizeMatter w={EACH_CONTAINER_WIDTH}>
                <View disableSizeMatter pos="relative" bottom={-12.5} style={{ transform: [{ scaleX: 1.05 }] }}>
                  <View h={45} w={45} pos="absolute" style={{ transform: [{ translateX: '40%' }] }} br={100} disableSizeMatter flexCenterRow top={-25} bg="orange">
                    <Icon fill={fill} />
                  </View>
                  <BottomCurve
                    width={EACH_CONTAINER_WIDTH}
                    height={46}
                    fill2="transparent"
                    stroke={theme == 'dark' ? colors.negative10 : colors.orange}
                    fill={theme === 'dark' ? '#081012' : 'white'}
                  />
                </View>
              </View>
            )}
            <View flexCenterRow pt={10} disableSizeMatter style={{ backgroundColor: theme === 'dark' ? '#081012' : 'white' }} w={EACH_CONTAINER_WIDTH}>
              <Animated.Text
                style={[
                  {
                    fontSize: 12,
                    color: colors.neutral90,
                    fontWeight: active ? '500' : '400',
                  },
                ]}>
                {tab.title}
              </Animated.Text>
            </View>
          </Pressable>
        );
      })}
    </View>
  );
});

const AuthenticatedTabs = () => {
  return (
    <Tab.Navigator
      initialRouteName={SCREENS.CONVERSATIONS}
      screenOptions={{ headerShown: true, header: props => <DrawerHeaderComponent {...props} /> }}
      tabBar={props => <CustomAnimatedTabBar {...props} />}>
      <Tab.Screen name={SCREENS.CONVERSATIONS} component={ConversationsScreen} />
      <Tab.Screen name={SCREENS.FEED} component={FeedScreen} />
      <Tab.Screen name={SCREENS.JOURNAL} component={JournalScreen} />
      <Tab.Screen name={SCREENS.MOOD_TRACKER} component={MoodScreen} />
      <Tab.Screen name={SCREENS.PROFILE} component={MyProfileScreen} />
    </Tab.Navigator>
  );
};

export default AuthenticatedTabs;
