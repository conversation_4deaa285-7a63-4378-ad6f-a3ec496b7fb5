import { Button, Text, TextInput, View } from '@components/native';
import { Avatar, ScreenWrapper } from '@components/shared';
import { Switch } from '@components/shared/animated';
import { notify, useAuth } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import { Controller, useForm } from 'react-hook-form';
import { KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { useRoute } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { FeedScreensParamsList } from '@navigation/types';
import SCREENS from '@src/SCREENS';

export const CreatePost = () => {
  const route = useRoute<RouteProp<FeedScreensParamsList, typeof SCREENS.CREATE_NEW_POST>>();
  const { onSuccess: onSuccessCallback } = route.params || {};
  const { session } = useAuth();
  const { control, setValue, getValues } = useForm({
    defaultValues: {
      title: '',
      description: '',
      disableComments: false,
    },
  });
  const username = session!.user!.username;

  const { isMutating, mutateAsync } = useStitch('createPost', {
    mutationOptions: {
      onSuccess: async () => {
        notify.bottom('Post created successfully!');
        onSuccessCallback?.();
        router.back();
      },
      onError: error => {
        console.error('Create post error:', error);
      },
    },
  });

  return (
    <ScreenWrapper
      title={
        <Text ff="PlayfairDisplay-SemiBold" fs="20" fw="600">
          New Post
        </Text>
      }
      px={20}>
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} style={{ flex: 1 }}>
        <ScrollView>
          <View pt={26} flex={1}>
            <View fd="row" ai="center" mb={31}>
              <Avatar showProgressRing={false} size={50} borderWidth={1} />
              <Text ml={11} fw="600" fs="14" color="neutral80">
                @{username}
              </Text>
            </View>
            <Text fw="400" fs="12" color="purple500" ml={9} mb={4}>
              Title
            </Text>
            {/* Add minLength 10 */}
            <TextInput label="Add Title" labelType="background" maxLength={50} control={control} name="title" gapBottom={33} />
            <Text fw="400" fs="12" color="purple500" ml={9} mb={4}>
              Description
            </Text>
            {/* Add minLength 30 */}
            <TextInput label="Add Description" labelType="background" maxLength={1000} control={control} name="description" multiline={true} />
            <View fd="row" ai="center" jc="space-between" pt={25}>
              <Text fw="400" fs="12" color="neutral70">
                Disable Comments
              </Text>
              <Controller
                control={control}
                name="disableComments"
                render={({ field: { value, onChange } }) => (
                  <Switch
                    isActive={value}
                    onToggle={newValue => {
                      onChange(newValue);
                      setValue('disableComments', newValue);
                    }}
                  />
                )}
              />
            </View>
          </View>
        </ScrollView>
        <View pos="absolute" bottom={53} left={0} right={0}>
          <Button
            isFullWidth={true}
            isLoading={isMutating}
            onPress={() => mutateAsync({ title: getValues('title'), body: getValues('description'), disableComments: getValues('disableComments') })}>
            POST
          </Button>
        </View>
      </KeyboardAvoidingView>
    </ScreenWrapper>
  );
};
