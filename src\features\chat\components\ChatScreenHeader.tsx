import ChatHealBadgeIcon from '@assets/svgs/chat-heal-badge.svg';
import ThreeDotMenuIcon from '@assets/svgs/three-dot-menu-icon.svg';
import { Text, View } from '@components/native';
import { BounceTap } from '@components/shared/animated';
import { useBottomSheet } from '@components/shared/bottom-sheet/BottomSheet';
import { memo } from 'react';
import { Pressable } from 'react-native';
import { ChatInteractionSheetContent } from './ChatInteractionSheetContent';

export const CustomHeaderComponent = memo(() => {
  const { openSheet, Sheet: ChatInteractionSheet, closeSheet } = useBottomSheet();
  const { openSheet: openChatOptionsSheet, Sheet: ChatOptionsSheet, closeSheet: closeChatOptionsSheet } = useBottomSheet();

  return (
    <>
      <View flex={1} ai="center" fd="row" jc="space-between">
        <Text ml={10} color="neutral90" fw="600" fs="20">
          @sunshine
        </Text>
        <View display="flex" fd="row" gap={14}>
          <BounceTap onPress={openSheet}>
            <ChatHealBadgeIcon />
          </BounceTap>
          <BounceTap onPress={openChatOptionsSheet}>
            <ThreeDotMenuIcon />
          </BounceTap>
        </View>
      </View>
      <ChatOptionsSheet wrapWithSheetView>
        <View mih={200} p={14}>
          <Pressable onPress={() => closeChatOptionsSheet()}>
            <Text ta="center" color="negative50" fw="600" fs="14">
              Block
            </Text>
            <View h={1} bg="neutral20" my={14} />
          </Pressable>
          <Pressable onPress={() => closeChatOptionsSheet()}>
            <Text ta="center" color="negative50" fw="600" fs="14">
              Report
            </Text>
          </Pressable>
          <View h={50} />
          <Pressable onPress={() => closeChatOptionsSheet()}>
            <Text ta="center" color="purple700" fw="600" fs="14">
              Cancel
            </Text>
          </Pressable>
        </View>
      </ChatOptionsSheet>
      <ChatInteractionSheet wrapWithSheetView>
        <ChatInteractionSheetContent closeSheet={closeSheet} userId="123" username="subshine" />
      </ChatInteractionSheet>
    </>
  );
});
