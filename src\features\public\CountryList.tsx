import LocationPin from '@assets/svgs/gps-location.svg';
import { Pressable, Text, View } from '@components/native';
import { LoaderCentered } from '@components/shared/animated';
import { useBottomSheet } from '@components/shared/bottom-sheet/BottomSheet';
import { BottomSheetFlatList, BottomSheetView } from '@gorhom/bottom-sheet';
import useStitch from '@packages/useStitch';
import { CountryItem } from '@packages/useStitch/types/public-api-types';
import React from 'react';
import { TouchableOpacity } from 'react-native';

interface CountryList {
  closeSheet?: () => void;
  onSelect?: (country: CountryItem) => void;
}
export const CountryList: React.FC<CountryList> = ({ closeSheet, onSelect }) => {
  const { data, isLoading } = useStitch('getCountryList');

  return (
    <>
      {isLoading ? (
        <BottomSheetView>
          <LoaderCentered />
        </BottomSheetView>
      ) : (
        <BottomSheetFlatList
          data={data?.data}
          style={{ flex: 1 }}
          contentContainerStyle={{ paddingBottom: 50 }}
          renderItem={({ item, index }) => {
            return (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  onSelect?.(item);
                  closeSheet?.();
                }}>
                <View key={index} py={10} px={20}>
                  <Text>
                    {`${item.flag} `} {item.name}
                  </Text>
                </View>
              </TouchableOpacity>
            );
          }}
        />
      )}
    </>
  );
};

interface CountryListTextInput {
  onSelect?: (country: CountryItem) => void;
  initialValue?: string;
  labelType?: 'background' | 'top-left';
}
export const CountryListTextInput: React.FC<CountryListTextInput> = ({ labelType = 'top-left', initialValue, onSelect }) => {
  const { Sheet, openSheet, closeSheet } = useBottomSheet({ snapPoints: ['100%'] });
  return (
    <>
      <Pressable onPress={openSheet}>
        <View bg="neutral10" px={16} br={14} my={10} py={16}>
          <View display="flex" fd="row" jc="space-between" ai="center">
            <Text fs="12" fw="500" color={initialValue ? 'neutral80' : 'neutral70'}>
              {initialValue ?? (labelType == 'background' ? 'Country' : '')}
            </Text>
            <LocationPin />
          </View>
        </View>
      </Pressable>
      <Sheet title="Select Country">
        <CountryList onSelect={onSelect} closeSheet={closeSheet} />
      </Sheet>
    </>
  );
};
