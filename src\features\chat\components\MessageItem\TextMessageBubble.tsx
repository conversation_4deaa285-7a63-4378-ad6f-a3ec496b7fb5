import { Text, View } from '@components/native';
import { ChatMessage } from '@packages/useStitch/types';
import { Dimensions } from 'react-native';

const { width } = Dimensions.get('screen');

export const TextMessageBubble = ({ item, myUserId }: { item: ChatMessage; myUserId: string }) => {
  return (
    <View br={20} maw={width * 0.75} style={{ alignSelf: item.senderId == myUserId ? 'flex-end' : 'flex-start' }} px={16} py={10} bg={item.senderId == myUserId ? 'purple200' : 'lightBlue'}>
      <Text color="neutral80" fs="12" fw="500">
        {item.message}
      </Text>
    </View>
  );
};
