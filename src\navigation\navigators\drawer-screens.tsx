import BookmarkIcon from '@assets/svgs/bookmark-icon.svg';
import FaqsIcon from '@assets/svgs/faq-icon.svg';
import LogoutIcon from '@assets/svgs/logout-icon.svg';
import PrivacyPolicyIcon from '@assets/svgs/privacy-policy-icon.svg';
import SettingsIcon from '@assets/svgs/settings-icon.svg';
import TermsOfServiceIcon from '@assets/svgs/terms-of-service-icon.svg';
import { AnimatedPressable, Button, Pressable, Text, View } from '@components/native';
import { Avatar, useDialog } from '@components/shared';
import { Ripple } from '@components/shared/animated';
import { useAuth, useTheme } from '@context/index';
import { DrawerItem } from '@features/drawer/components/DrawerItem';
import { useBottomSheetModal } from '@gorhom/bottom-sheet';
import { router } from '@navigation/refs/navigation-ref';
import { DrawerContentComponentProps, createDrawerNavigator, useDrawerStatus } from '@react-navigation/drawer';
import { Dimensions, Image, ScrollView } from 'react-native';
import { useAnimatedStyle, withTiming } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AuthenticatedTabs from './auth-tabs';
const Drawer = createDrawerNavigator();
const { width } = Dimensions.get('screen');

const DrawerScreens = () => {
  return (
    <Drawer.Navigator
      drawerContent={props => <CustomDrawerComponent {...props} />}
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          maxWidth: width * 0.8,
          width: width * 0.8,
        },
      }}>
      <Drawer.Screen name="tab" component={AuthenticatedTabs} options={{ title: 'Home' }} />
    </Drawer.Navigator>
  );
};

export default DrawerScreens;

const DrawerHeader = () => {
  const { colors } = useTheme();
  const DRAWER_HEADER_WIDTH = width * 0.8;
  const DRAWER_HEADER_HEIGHT = 200;
  const isOpened = useDrawerStatus() == 'open';
  const { top } = useSafeAreaInsets();
  const name = 'Lucas Benjamin Scott';
  const username = useAuth().session!.user!.username;

  return (
    <>
      {isOpened && (
        <View disableSizeMatter>
          <Image
            style={{ width: DRAWER_HEADER_WIDTH, height: DRAWER_HEADER_HEIGHT, borderBottomLeftRadius: 20, borderBottomRightRadius: 20 }}
            source={require('@assets/images/drawer-bg.png')}
          />
          <View disableSizeMatter pos="absolute" top={0} pt={top} display="flex" jc="flex-end" px={20} pb={20} h={DRAWER_HEADER_HEIGHT} w={DRAWER_HEADER_WIDTH} left={0}>
            <Pressable onPress={router.closeDrawer} pos="absolute" top={top + 26} right={21}>
              <Ionicons size={22} color={colors.neutral80} name="close" />
            </Pressable>
            <View display="flex" fd="row" ai="center">
              <Avatar showProgressRing={false} />
              <View px={10}>
                <Text fs="10" color="neutral80" fw="400">
                  {name}
                </Text>
                <Text fs="12" color="neutral80" fw="600">
                  @{username}
                </Text>
              </View>
            </View>
          </View>
        </View>
      )}
    </>
  );
};

const CustomDrawerComponent = ({ ...props }: DrawerContentComponentProps) => {
  const { dismissAll } = useBottomSheetModal();
  const { theme, colors, sheetValue } = useTheme();

  // Animated style for the overlay
  const overlayStyle = useAnimatedStyle(() => ({
    opacity: withTiming(sheetValue.get() ? 0.2 : 0, { duration: 300 }),
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'black',
    zIndex: 5, // Below bottom sheet (zIndex > 10)
    pointerEvents: sheetValue.get() ? 'auto' : 'none',
  }));
  return (
    <View display="flex" fd="column" jc="space-between" style={{ height: '100%' }}>
      <AnimatedPressable onPress={dismissAll} style={overlayStyle} />
      <DrawerHeader />
      <View flex={1} mt={32}>
        <ScrollView bounces={false}>
          <DrawerItem onPress={() => router.navigate('Bookmarks')} title="Saved Posts" icon={<BookmarkIcon />} />
          <DrawerItem onPress={() => router.navigate('Faqs')} title="FAQs" icon={<FaqsIcon />} />
          <DrawerItem onPress={() => router.navigate('Settings')} title="Settings" icon={<SettingsIcon />} />
        </ScrollView>
      </View>
      <DrawerFooter />
    </View>
  );
};

const DrawerFooter = () => {
  const { Dialog, closeDialog, openDialog } = useDialog();
  const { bottom } = useSafeAreaInsets();
  const { handleLogout, isLoggingOut } = useAuth();

  return (
    <View pb={bottom} bc="neutral00">
      <View mx={24} h={0.5} bg="neutral00" />
      <DrawerItem icon={<LogoutIcon />} onPress={openDialog} title="Logout" noBorder />
      <DrawerItem icon={<PrivacyPolicyIcon />} onPress={() => router.navigate('Login')} title="Privacy Policy" noBorder />
      <DrawerItem icon={<TermsOfServiceIcon />} onPress={() => router.navigate('Login')} noBorder title="Terms of Services" />
      <Dialog>
        <View p={20}>
          <Text ta="center" fw="600" fs="24" ff="PlayfairDisplay-SemiBold">
            Are you sure you want to logout ?
          </Text>
          <View mt={30} mb={20} gap={10} flexCenterRow>
            <Button
              isLoading={isLoggingOut}
              onPress={() => {
                closeDialog();
                handleLogout?.();
              }}
              isHorizontalAligned>
              Yes
            </Button>
            <Button bg="background" color="purple600" bw={1} bc="purple600" onPress={closeDialog} isHorizontalAligned>
              No
            </Button>
          </View>
        </View>
      </Dialog>
    </View>
  );
};
