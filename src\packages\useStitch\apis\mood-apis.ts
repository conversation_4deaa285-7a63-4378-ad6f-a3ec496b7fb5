import { CACHE_APIS } from '../api-base-cache-keys';
import { GetMoodCalenderResponse, GetMoodWeekResponse, LogMoodResponse } from '../types';
import { ApiResponse, MutationApiConfig } from '../types/common-api-types';

export namespace MoodApis {
    export const logMood = 'logMood' as const;
    export const getMoodWeek = 'getMoodWeek' as const;
    export const getMoodCalender = 'getMoodCalender' as const;
}

const C = CACHE_APIS.MOOD

export const moodApiConfig = {
    logMood: {
        path: '/mood/log',
        method: 'POST',
        protected: true,
        mutationBody: undefined as unknown as any,
        responseType: undefined as unknown as ApiResponse<LogMoodResponse>,
        baseCacheKey: C.log_mood,
        staleTime: 0,
        type: 'mutation' as const,
    } satisfies MutationApiConfig<LogMoodResponse, any>,
    getMoodWeek: {
        path: '/mood/week',
        method: 'GET',
        protected: true,
        responseType: undefined as unknown as ApiResponse<GetMoodWeekResponse>,
        baseCacheKey: C.get_mood_week,
        staleTime: 0,
        type: 'mutation' as const,
    } satisfies MutationApiConfig<GetMoodWeekResponse, any>,
    getMoodCalender: {
        path: '/mood/calendar',
        method: 'GET',
        protected: true,
        responseType: undefined as unknown as ApiResponse<GetMoodCalenderResponse>,
        baseCacheKey: C.get_mood_calender,
        staleTime: 0,
        type: 'mutation' as const,
    } satisfies MutationApiConfig<GetMoodCalenderResponse, any>,
};
