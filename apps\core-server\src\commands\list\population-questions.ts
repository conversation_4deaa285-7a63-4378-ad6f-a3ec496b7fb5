import { Command } from "@/lib/command-module.js";
import db from "@repo/db";
import { logger } from "@repo/lib";
import { OnboardRepo } from "@repo/repo";

const onboardingConfig = {
  skipEnabled: true,
  questions: [
    {
      id: "90128s0912is",
      heading: "What kind of pain you are going through?",
      subHeading: "Select at least one type you would like us to help you with",
      options: [
        { id: "partiluar_answer_id", title: "Breakup" },
        { id: "partiluar_answer_id", title: "Family Loss" },
        { id: "partiluar_answer_id", title: "Divorce" },
        { id: "partiluar_answer_id", title: "Pet Loss" },
        { id: "partiluar_answer_id", title: "Financial Loss" },
        { id: "partiluar_answer_id", title: "Lost Someone" },
        { id: "partiluar_answer_id", title: "Dont want to share" },
      ],
      multiSelect: true,
      maxItemOnScreen: 4,
    },
    {
      id: "90128s0912is",
      heading: "Time since the loss occurred",
      subHeading: "Select at least one type you would like us to help you with",
      options: [
        { id: "partiluar_answer_id", title: "Less than a month" },
        { id: "partiluar_answer_id", title: "Less than 3 months" },
        { id: "partiluar_answer_id", title: "Less than 6 months" },
        { id: "partiluar_answer_id", title: "Less than a year" },
      ],
      multiSelect: false,
    },
  ],
};

export const addQuestions = new Command({
  name: "add-questions",
  description: "Add Questions",
  fn: async () => {
    logger.info("Task Started");
    await db.transaction(async (tx) => {
      await Promise.all(
        onboardingConfig.questions.map((question) =>
          OnboardRepo.createQuestion(
            {
              title: question.heading,
              description: question.subHeading,
              options: question.options.map((option) => option.title),
              questionType: question.multiSelect
                ? "multiple-select"
                : "single-select",
            },
            tx,
          ),
        ),
      );
    });
    logger.info("Task Done");
  },
});
