import ClockIcon from '@assets/svgs/clock-icon.svg';
import SearchIcon from '@assets/svgs/search-icon.svg';
import { Button, Pressable, Text, TextInput, View } from '@components/native';
import { FlashList, ScreenWrapper } from '@components/shared';
import { LoaderCentered, Ripple } from '@components/shared/animated';
import { useTheme } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import React from 'react';
import { useForm } from 'react-hook-form';
import { Dimensions } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useJournalStore } from './store/journal-store';
const { width } = Dimensions.get('screen');

export const SearchJournal = () => {
  const { colors } = useTheme();
  const { data, isLoading, refetch: refetchHistory } = useStitch('journalSearchHistoryList');
  const { journalSearchQuery, setJournalSearchQuery } = useJournalStore();
  const { refetch: refetchJournalCategory } = useStitch('journalCategoryList', { queryOptions: { select: false } as any });
  const { bottom } = useSafeAreaInsets();
  const { control, handleSubmit } = useForm({
    defaultValues: {
      query: journalSearchQuery ?? '',
    },
  });

  const onSubmit = ({ query }: { query: string }) => {
    setJournalSearchQuery(query);
    router.back();
    refetchJournalCategory().then(() => refetchHistory());
  };

  const searchInput = (
    <View flex={1} my={10} px={20}>
      <TextInput
        autoFocus
        control={control}
        name="query"
        label="Search"
        labelType="background"
        gapBottom={5}
        onSubmitEditing={e => {
          // this should be called when user taps of search in keyboard
          // call search function from here or when user taps on search button below..
          console.log('Submitted >>>');
        }}
        suffixIconAlwaysVisible
        returnKeyType="search"
        suffixIcon={<SearchIcon style={{ marginRight: 5 }} stroke={colors.neutral40} />}
        prefixIcon={
          <Pressable pt={2} onPress={router.back}>
            <Ionicons name="arrow-back-outline" color={colors.neutral70} size={22} />
          </Pressable>
        }
      />
    </View>
  );

  return (
    <ScreenWrapper
      headerStyles={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}
      leftHeaderProps={{ px: 0 }}
      hideBackButton
      customHeaderComponent={searchInput}
      py={20}>
      {isLoading ? (
        <LoaderCentered />
      ) : data?.data.length == 0 ? (
        <View flex={1} flexCenterRow>
          <Text mt={-80} fs="14" fw="500" color="neutral80">
            No Search History
          </Text>
        </View>
      ) : (
        <FlashList
          hideHeader
          contentContainerStyle={{ paddingBottom: 100 }}
          data={data?.data}
          renderItem={({ index, item }) => {
            return (
              <Ripple onPress={() => onSubmit({ query: item.text })} key={index}>
                <View px={20} pt={10} display="flex" fd="column" gap={8}>
                  <View fd="row" display="flex" ai="center" gap={10}>
                    <ClockIcon stroke={colors.neutral60} />
                    <Text>{item.text}</Text>
                  </View>
                  <View h={1} mt={4} bg="neutral10" />
                </View>
              </Ripple>
            );
          }}
        />
      )}

      <View disableSizeMatter px={20} pos="absolute" w={width} bottom={bottom > 10 ? bottom : bottom + 10}>
        <Button onPress={handleSubmit(onSubmit)}>Search</Button>
      </View>
    </ScreenWrapper>
  );
};
