import type { RootNavigatorParamList } from '@navigation/types';
import { LinkingOptions } from '@react-navigation/native';
import SCREENS from '@src/SCREENS';

export const linking: LinkingOptions<RootNavigatorParamList> = {
  prefixes: ['stitch://', 'https://stitch.com'],
  config: {
    screens: {
      // 🔓 Public
      [SCREENS.FAQS]: 'faqs',
      [SCREENS.PRIVACY_POLICY]: 'privacy-policy',
      [SCREENS.TERMS_OF_SERVICE]: 'terms-of-service',

      // 🟡 Non-auth
      [SCREENS.INTRO]: 'intro',
      [SCREENS.PRE_AUTH]: 'pre-auth',
      [SCREENS.SIGNUP]: 'signup',
      [SCREENS.LOGIN]: 'login',
      [SCREENS.VERIFY_OTP]: {
        path: 'verify-otp',
        parse: {
          email: (email: string) => email,
          type: (type: string) => type as 'login' | 'signup',
        },
      },

      // ✅ Chat
      [SCREENS.CONVERSATIONS]: 'conversations',
      [SCREENS.CHAT]: {
        path: 'conversations/chat',
        parse: {
          firstName: (s: string) => s,
          userId: (s: string) => s,
          conversationId: (s: string) => s,
        },
      },

      // ✅ Feed
      [SCREENS.FEED]: 'feed',
      [SCREENS.CREATE_NEW_POST]: 'feed/post/create',
      [SCREENS.CREATE_NEW_POLL]: 'feed/poll/create',
      [SCREENS.EDIT_POST]: {
        path: 'feed/post/edit',
        parse: { postId: (s: string) => s },
      },
      [SCREENS.EDIT_POLL]: {
        path: 'feed/poll/edit',
        parse: { postId: (s: string) => s },
      },

      // ✅ Journal
      [SCREENS.JOURNAL]: 'journal',
      [SCREENS.JOURNAL_SEARCH]: 'journal/search',
      [SCREENS.ADD_JOURNAL]: 'journal/add',
      [SCREENS.EDIT_JOURNAL]: {
        path: 'journal/edit',
        parse: { journalId: (s: string) => s },
      },

      // ✅ Mood
      [SCREENS.MOOD_TRACKER]: 'mood-tracker',
      [SCREENS.MOOD_CALENDAR]: 'mood-tracker/calendar',
      [SCREENS.MOOD_ARTICLE]: 'mood-tracker/article',

      // ✅ Profile
      [SCREENS.PROFILE]: 'profile',
      [SCREENS.EDIT_PROFILE]: 'profile/edit',
      [SCREENS.SELECT_AVATAR]: 'profile/edit/avatars',
      [SCREENS.USERS_PROFILE]: {
        path: 'profile/user',
        parse: { userId: (s: string) => s },
      },

      // ✅ Drawer
      [SCREENS.BOOKMARKS]: 'bookmarks',
      [SCREENS.SETTINGS]: 'settings',
      [SCREENS.BLOCKED_ACCOUNTS]: 'settings/blocked-accounts',
    },
  },
};
