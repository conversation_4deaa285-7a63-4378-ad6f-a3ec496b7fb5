import { Button, Text, View } from '@components/native';
import { Avatar, ScreenWrapper } from '@components/shared';
import React from 'react';
import { ScrollView } from 'react-native-gesture-handler';

const BlockedAccount = () => {
  return (
    <View px={24} py={16} fd="row" ai="center" jc="space-between">
      <View fd="row" ai="center" gap={10}>
        <Avatar size={40} showProgressRing={false} />
        <Text ff="Montserrat-SemiBold" fs="12" fw="600" color="neutral80">
          @username
        </Text>
      </View>
      <Button fs="12" fw="600" onPress={() => {}}>
        Unblock
      </Button>
    </View>
  );
};

export const BlockedAccountsScreen = () => {
  return (
    <ScreenWrapper
      title={
        <Text ff="PlayfairDisplay-SemiBold" fs="20" fw="600">
          Blocked Accounts
        </Text>
      }>
      <View display="flex" style={{ height: '100%' }} flex={1}>
        <ScrollView bounces={false} contentContainerStyle={{ paddingTop: 16, paddingBottom: 16 }}>
          <BlockedAccount />
          <BlockedAccount />
          <BlockedAccount />
          <BlockedAccount />
        </ScrollView>
      </View>
    </ScreenWrapper>
  );
};
