import { ThemeColors } from '@/types/color-types';
import DeleteIcon from '@assets/svgs/delete-icon.svg';
import PinIcon from '@assets/svgs/pin-icon.svg';
import SearchIcon from '@assets/svgs/search-icon.svg';
import { AnimatedText, AnimatedView, Button, Image, Text, View } from '@components/native';
import { FlashList } from '@components/shared';
import { BounceTap, Ripple } from '@components/shared/animated';
import { BestStichersHorizontalSkeleton } from '@components/shared/animated/skeletons';
import { useBottomSheet } from '@components/shared/bottom-sheet/BottomSheet';
import { notify, useTheme } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import { BestStitcherItem, ChatRequestsItem, ConversationItem } from '@packages/useStitch/types';
import { MotiView } from 'moti';
import React, { memo, useCallback, useMemo, useState } from 'react';
import { Dimensions, FlatList, Pressable, SafeAreaView, StyleSheet } from 'react-native';
import { FadeInRight, FadeOutLeft, LayoutAnimationConfig, LinearTransition } from 'react-native-reanimated';
import { useConversationStore } from './store/useConversationStore';

const { width } = Dimensions.get('screen');

export const ConversationsScreen = memo(() => {
  useStitch('badgesList');
  const { colors } = useTheme();
  const { currentTab } = useConversationStore();
  const { refetch: refetchBestStitchers } = useStitch('bestStitchers');
  const { data: conversationList, isError, isInitialLoading } = useStitch('conversationsList');
  const { data: pendingRequests, refetch: refetchPendingRequests } = useStitch('chatPendingRequestsList');

  const contentContainerStyle = {
    paddingBottom: 80,
  };

  const handleRefresh = () => {
    refetchBestStitchers();
    refetchPendingRequests();
  };

  return (
    <FlashList
      onRefresh={handleRefresh}
      ItemSeparatorComponent={() => <View h={8} />}
      refreshing={false}
      hideHeader
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={<ConversationsHeader />}
      data={currentTab == 'chat' ? conversationList?.data ?? [] : pendingRequests?.data ?? ([] as any)}
      ListEmptyComponent={<ListEmptyComponent />}
      contentContainerStyle={contentContainerStyle}
      renderItem={({ item, index }) => <RenderItem item={item} key={`${index}-${currentTab}`} />}
    />
  );
});

const ListEmptyComponent = () => {
  const { currentTab } = useConversationStore();

  return (
    <View>
      <Text fs="12" ta="center" mt={20}>
        {currentTab == 'chat' ? "You haven't started any conversation yet." : "You don't have any pending requests."}
      </Text>
    </View>
  );
};

const RenderItem = ({ item }: { item: any }) => {
  const { currentTab } = useConversationStore();
  const { colors } = useTheme();

  if (currentTab == 'requests') {
    return <RequestItem item={item as any} />;
  }

  return <ChatItem colors={colors} item={item as any} />;
};

const ConversationsHeader = memo(() => {
  const { colors } = useTheme();
  const { data, isLoading, isError } = useStitch('bestStitchers', { queryOptions: { enabled: false } as any });

  const contentContainerStyle = useMemo(
    () => ({
      paddingHorizontal: 20,
      marginBottom: 20,
      paddingEnd: width / 6,
    }),
    [],
  );

  return (
    <View bg="white">
      <View px={20} pt={14}>
        <Pressable onPress={() => router.navigate('ChatSearch')}>
          <View bg="white" bc="purpleLight" bw={1} display="flex" fd="row" jc="space-between" br={16} px={20} py={14}>
            <Text color="neutral40" fs="14">
              Search
            </Text>
            <SearchIcon stroke={colors.purple600} />
          </View>
        </Pressable>
        <Text my={14} ml={3} fs="14" fw="600" color="neutral70">
          Best Stitchers
        </Text>
      </View>
      {isLoading ? (
        <BestStichersHorizontalSkeleton />
      ) : isError || data?.data?.length === 0 ? (
        <View px={20} mb={20}>
          <Text ta="center" fs="12" color="neutral70">
            No best stitcher at the moment
          </Text>
        </View>
      ) : (
        <FlatList
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={contentContainerStyle}
          ItemSeparatorComponent={() => <View w={12} />}
          data={data?.data ?? []}
          renderItem={({ item, index }) => <BestStitcherCard item={item} key={index} />}
        />
      )}
      <TabViewSwitcher />
    </View>
  );
});

const TabViewSwitcher = () => {
  const { currentTab, setCurrentTab } = useConversationStore();
  const { data } = useStitch('chatPendingRequestsList', {
    queryOptions: { select: false } as any,
  });

  return (
    <View display="flex" pl={20} mb={14} ai="center" gap={10} px={14} fd="row">
      <TabHeader onPress={() => setCurrentTab('chat')} title="Chats" count={0} isActive={currentTab === 'chat'} />
      <TabHeader onPress={() => setCurrentTab('requests')} title="Requests" count={data?.pagination?.total ?? 0} isActive={currentTab === 'requests'} />
    </View>
  );
};

const BestStitcherCard = ({ item }: { item: BestStitcherItem }) => {
  const { isMutating, mutateAsync } = useStitch('chatRequestUser', {
    urlBuilder: path => `${path}/${item.id}`,
    mutationOptions: {
      onSuccess: () => notify.bottom('Request sent for chat'),
    },
  });

  return (
    <View shadow="md" bg="white" mih={120} br={14} bc="neutral10" bw={1} mah={180} miw={130} maw={150} py={10} display="flex" fd="column" jc="center" ai="center">
      <View>
        <View size={64} mb={20} br={100} bg="orange">
          <Image hideErrorText source={{ uri: item.avatar }} size={64} />
        </View>
        <View bg="lightBlue" pos="absolute" bottom={2} px={5} py={3} br={7} bw={1} bc="neutral20">
          <Text fs="10">{item.totalHealCount.toString()} Healed</Text>
        </View>
      </View>
      <View mt={5} px={10}>
        <Text numberOfLines={1} fs="12" fw="500" color="neutral80">
          {item.username}
        </Text>
      </View>
      <View flexCenterRow mt={10} px={10}>
        <Button
          h={32}
          py={0}
          bg="purpleLight"
          br={10}
          isLoading={isMutating}
          color="neutral80"
          isHorizontalAligned
          onPress={() => {
            console.log('>>>REUQESTER USR ', item.id);
            // mutateAsync
          }}>
          Chat
        </Button>
      </View>
    </View>
  );
};

type TabHeaderProps = {
  title: string;
  count: number;
  isActive: boolean;
  onPress?: () => void;
};

const TabHeader = memo(({ count = 0, onPress, isActive = false, title }: TabHeaderProps) => {
  return (
    <BounceTap onPress={onPress}>
      <View bc="neutral20" style={{ backgroundColor: isActive ? '#FEEBE491' : '' }} flexCenterRow gap={5} bw={1} px={25} py={4} br={50}>
        <Text fs="12" color="neutral80" fw="600">
          {title}
        </Text>
        {count > 0 && (
          <View bg="orange" p={2} br={50} disableSizeMatter miw={20} flexCenterRow>
            <Text fs="8">{count}</Text>
          </View>
        )}
      </View>
    </BounceTap>
  );
});

const AVATAR_SIZE = 45;

type ChatItemProps = {
  item: ConversationItem;
  colors: ThemeColors;
};

const ChatItem = memo(({ item, colors }: ChatItemProps) => {
  const { Sheet, closeSheet, openSheet } = useBottomSheet();
  const {} = useStitch('bestStitchers', { queryOptions: { select: false } as any });
  const { refetch: refetchConversationList } = useStitch('conversationsList', { queryOptions: { select: false } as any });

  const { mutateAsync, isMutating } = useStitch('pinUnpinChat', {
    urlBuilder: path => `${path}/${item.roomId}`,
    mutationOptions: {
      onSuccess: () => {
        refetchConversationList();
      },
    },
  });

  const handlePress = useCallback(() => {
    router.navigate('Chat');
  }, []);

  return (
    <>
      <Ripple onLongPress={openSheet} onPress={handlePress} rippleColor={colors.neutral10}>
        <View disableSizeMatter display="flex" fd="row" ai="center" w={width} mih={50} mah={80} px={20} py={10}>
          <Image source={{ uri: item.avatar }} hideErrorText size={AVATAR_SIZE} br={100} bw={1} bc="neutral10" />
          <View disableSizeMatter flex={1} display="flex" fd="row" jc="space-between" ai="center" px={10} gap={10}>
            <View display="flex" fd="column" gap={4} flexGrow={1} flexShrink={1} maw={width - AVATAR_SIZE - 80}>
              <Text color="neutral80" fs="12" fw="600">
                {item.username}
              </Text>
              <Text numberOfLines={1} color="neutral50" fs="14">
                {/* {item.lastMsg} */}
                Last Mock Message
              </Text>
            </View>
            <View disableSizeMatter display="flex" fd="row" ai="center" gap={10}>
              {/* {item.unreadMsgCount > 0 && (
              <View size={20} flexCenterRow br={100} bg="orange">
              <Text color="neutral80" fs="12">
              {item.unreadMsgCount}
              </Text>
              </View>
              )} */}
              {item.isPinned && <PinIcon />}
            </View>
          </View>
        </View>
      </Ripple>
      <Sheet isMutating={isMutating} wrapWithSheetView>
        <View mih={200} p={14}>
          <Pressable onPress={() => mutateAsync({ pin: !item.isPinned })}>
            <View display="flex" fd="row" ai="center" jc="space-between">
              <Text color="neutral80" fw="500" fs="14">
                {item.isPinned ? 'Unpin' : 'Pin'} this chat{' '}
              </Text>
              <PinIcon height={18} width={18} />
            </View>
            <View h={1} bg="neutral20" my={14} />
          </Pressable>
          <Pressable onPress={() => closeSheet()}>
            <View display="flex" fd="row" ai="center" jc="space-between">
              <Text color="negative50" fw="500" fs="14">
                Clear Chat
              </Text>
              <DeleteIcon fill={colors.negative50} height={18} width={18} />
            </View>
          </Pressable>
        </View>
      </Sheet>
    </>
  );
});

const RequestItem = ({ item }: { item: ChatRequestsItem }) => {
  const { refetch } = useStitch('chatPendingRequestsList', { queryOptions: { select: false } as any });
  const { isMutating: isAcceptMutating, mutate } = useStitch('chatAcceptPendingRequest', {
    urlBuilder: path => `${path}/${item.id}`,
    mutationOptions: {
      onSuccess: refetch,
    },
  });

  return (
    <View disableSizeMatter display="flex" fd="row" ai="center" w={width} mih={50} mah={80} px={20} py={10}>
      <View size={AVATAR_SIZE} br={100} bg="orange" />
      <View disableSizeMatter flex={1} display="flex" fd="row" jc="space-between" ai="center" px={10} gap={10}>
        <Text color="neutral80" fs="12" fw="600">
          {item.user.username}
        </Text>
        <View flexCenterRow gap={10}>
          <Button isLoading={isAcceptMutating} px={14} h={36} onPress={() => mutate({})}>
            <Text color="white" fw="600" fs="12">
              Accept
            </Text>
          </Button>
          <Button disabled={isAcceptMutating} px={14} h={36} bg="background" bc="neutral20" bw={1} onPress={() => {}}>
            <Text fs="12" fw="600" color="purple700">
              Reject
            </Text>
          </Button>
        </View>
      </View>
    </View>
  );
};

type TabItem = {
  label: string;
};

type TabsProps = {
  data: TabItem[];
  selectedIndex: number;
  onChange: (index: number) => void;
  activeColor?: string;
  inActiveColor?: string;
  activeBackgroundColor?: string;
  inActiveBackgroundColor?: string;
};

const _spacing = 4;
const Tabs: React.FC<TabsProps> = ({ data, selectedIndex, onChange, activeBackgroundColor = '#111', inActiveBackgroundColor = '#ddd', activeColor = '#fff', inActiveColor = '#999' }) => {
  return (
    <View style={{ flexDirection: 'row', gap: _spacing }}>
      {data.map((item, index) => {
        const isSelected = selectedIndex == index;
        return (
          <MotiView
            animate={{
              backgroundColor: isSelected ? activeBackgroundColor : inActiveBackgroundColor,
              borderRadius: 8,
              overflow: 'hidden',
            }}
            layout={LinearTransition.springify().damping(80).stiffness(200)}
            key={index}>
            <Pressable
              style={{
                padding: _spacing * 2,
                justifyContent: 'center',
                alignItems: 'center',
                gap: _spacing,
                flexDirection: 'row',
              }}
              onPress={() => onChange(index)}>
              <AnimatedText
                entering={FadeInRight.springify().damping(8).stiffness(200)}
                style={{ color: isSelected ? activeColor : inActiveColor, fontWeight: isSelected ? 'bold' : 'normal' }}>
                {item.label}
              </AnimatedText>
            </Pressable>
          </MotiView>
        );
      })}
    </View>
  );
};

const tabs = ['#ff005c', '#ffbd00'];
export const ConversationsScreen1 = () => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  return (
    <SafeAreaView style={styles.container}>
      <Tabs
        data={[
          {
            label: 'Chats',
          },
          {
            label: 'Requests',
          },
        ]}
        onChange={index => setSelectedIndex(index)}
        selectedIndex={selectedIndex}
      />
      <LayoutAnimationConfig skipEntering>
        <AnimatedView
          key={`tab-context-${selectedIndex}`}
          entering={FadeInRight.springify().damping(80).stiffness(200)}
          exiting={FadeOutLeft.springify().damping(80).stiffness(200)}
          style={{
            backgroundColor: tabs[selectedIndex],
            flex: 1,
            marginHorizontal: 10,
            marginBottom: 70,
            borderRadius: 20,
          }}
        />
      </LayoutAnimationConfig>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    gap: 12,
    justifyContent: 'center',
  },
});
