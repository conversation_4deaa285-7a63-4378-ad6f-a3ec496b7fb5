import { Button, OtpInput, Text, View } from '@components/native';
import { StackBackButton } from '@components/shared';
import { LoaderCentered } from '@components/shared/animated';
import { useAuth } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import React, { useEffect, useState } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import IoniIcons from 'react-native-vector-icons/Ionicons';
import AnimatedAuthPipeAnimationWrapper from './components/AnimatedAuthPipeAnimationWrapper';

export const VerifyOtp = () => {
  const params = router.params('VerifyOtp');
  const { handleVerifyOtpSuccess } = useAuth();
  const { top } = useSafeAreaInsets();
  const { mutateAsync: mutateLogin, isMutating: isLoginMutating } = useStitch('login');
  const { mutateAsync: mutateSignup, isMutating: isSignupMutating } = useStitch('signup');

  const { isMutating, mutateAsync } = useStitch('verifyOtp', {
    mutationOptions: {
      onSuccess: handleVerifyOtpSuccess,
    },
  });

  const isResendMutating = params?.type == 'login' ? isLoginMutating : isSignupMutating;

  const [otp, setOtp] = useState('');
  const [timer, setTimer] = useState(30); // Start timer at 30 seconds
  const [isTimerActive, setIsTimerActive] = useState(true);

  // Start or restart the timer
  const startTimer = () => {
    setTimer(30);
    setIsTimerActive(true);
    const interval = setInterval(() => {
      setTimer(prev => {
        if (prev <= 0) {
          setIsTimerActive(false);
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    return interval;
  };

  // Timer initialization
  useEffect(() => {
    const interval = startTimer();
    return () => clearInterval(interval);
  }, []);

  // Resend OTP handler
  const handleResendCode = async () => {
    if (isTimerActive || !params?.email) return;

    try {
      const mutation = params.type === 'login' ? mutateLogin : mutateSignup;
      await mutation({ email: params.email });
      startTimer();
    } catch (error) {
      console.error('Failed to resend OTP:', error);
    }
  };

  return (
    <AnimatedAuthPipeAnimationWrapper>
      <View flex={1} p={20}>
        {router.canGoBack() && <StackBackButton title="Enter authentication code" />}
        <View mt={top + 50} display="flex" fd="column" w="100%">
          <View display="flex" fd="column">
            <Text fw="500" color="neutral80">
              A 4-digit code was sent to
            </Text>
            <Text
              fw="400"
              color="purple700"
              style={{
                flexWrap: 'wrap',
                width: '100%',
              }}>
              {params?.email}
            </Text>
          </View>

          <Text
            onPress={router.back}
            fw="400"
            color="purple700"
            style={{
              alignSelf: 'flex-end',
            }}>
            Change email
          </Text>
        </View>
        <View mt={40}>
          <OtpInput onOtpChange={otp => setOtp(otp)} />
          {isResendMutating ? (
            <View mt={30}>
              <LoaderCentered />
            </View>
          ) : (
            <Text
              onPress={handleResendCode}
              ta="center"
              fw="600"
              color={isTimerActive ? 'neutral40' : 'neutral80'} // Gray out when timer is active
              my={14}
              style={{ opacity: isTimerActive ? 0.5 : 1 }} // Visual feedback for disabled state
              disabled={isTimerActive} // Disable press when timer is active
            >
              {isTimerActive ? `Resend Code (${timer}s)` : 'Resend Code'}
            </Text>
          )}
          <Button
            isLoading={isMutating}
            onPress={() => {
              mutateAsync({
                email: params?.email!,
                otp,
              });
            }}
            mt={50}
            h={48}
            isDisabled={otp.length < 4}
            rightIcon={<IoniIcons name="arrow-forward" color="white" size={20} />}>
            Continue
          </Button>
        </View>
      </View>
    </AnimatedAuthPipeAnimationWrapper>
  );
};
