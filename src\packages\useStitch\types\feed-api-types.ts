// Feed API Types
export interface CreatePostMutationBody {
    title: string;
    body: string;
    disableComments?: boolean;
}

export interface CreatePollMutationBody {
    title: string;
    options: string[];
    multiSelect?: boolean;
    disableComments?: boolean;
}

export interface VotePollMutationBody {
    optionId: string;
}

export interface PollOption {
    id: string;
    option: string;
    votes: number;
    total: number;
    voted: boolean;
}

export interface FeedItem {
    id: string;
    type: 'post' | 'poll';
    title: string;
    createdAt: string;
    body: string | null;
    poll: PollOption[] | null;
    multiSelect?: boolean;
    disableComments: boolean;
    liked: boolean;
    bookmarked: boolean;
    voted?: boolean;
    metrics: {
        totalLikes: number;
        totalComments: number;
    };
    user: {
        id: string;
        username: string;
        avatar: string;
    };
}

export interface FeedListResponse {
    items: FeedItem[];
    hasMore: boolean;
}

export interface FeedQueryParams {
    page?: number;
    limit?: number;
    type?: 'post' | 'poll';
    userId?: string;
}

export interface LikeFeedItemResponse {
    message: string;
}

export interface BookmarkFeedItemResponse {
    message: string;
}

export interface VotePollResponse {
    data: string;
}