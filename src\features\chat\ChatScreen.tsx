import { View } from '@components/native';
import { FlatList } from '@components/shared';
import { useAuth } from '@context/index';
import { ChatMessage } from '@packages/useStitch/types';
import { CustomHeaderComponent } from './components/ChatScreenHeader';
import { MessageItemRenderer } from './components/MessageItem/MessageItemRenderer';
import { SendMessageInput } from './components/SendMessageInput';
import { useMessageStore } from './store/useMessageStore';

export const ChatScreen = () => {
  const { session } = useAuth();
  const { messages, flatListRef } = useMessageStore();
  const myUserId = session?.user?.id ?? '';

  return (
    <FlatList
      ref={flatListRef}
      inverted={true}
      data={messages}
      customHeaderComponent={<CustomHeaderComponent />}
      customFooterComponent={<SendMessageInput />}
      maintainVisibleContentPosition={{
        minIndexForVisible: 0,
      }}
      ItemSeparatorComponent={() => <View h={10} />}
      contentContainerStyle={{ paddingHorizontal: 14, paddingTop: 10 }}
      renderItem={({ index, item }: { index: number; item: ChatMessage }) => {
        return <MessageItemRenderer myUserId={myUserId} item={item} key={index} />;
      }}
    />
  );
};
