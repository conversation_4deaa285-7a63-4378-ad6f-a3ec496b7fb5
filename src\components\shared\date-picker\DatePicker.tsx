// DatePicker.tsx
import { But<PERSON>, Text, View } from '@components/native';
import { notify } from '@context/index';
import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { Pressable, StyleSheet } from 'react-native';
import Animated, { runOnJS, useAnimatedScrollHandler, useAnimatedStyle, useSharedValue } from 'react-native-reanimated';
import { Ripple } from '../animated';
import { useBottomSheet } from '../bottom-sheet/BottomSheet';

const AnimatedScrollView = Animated.createAnimatedComponent(BottomSheetScrollView);

// Constants
const MONTHS = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
const MIN_YEAR = 1900;
const MAX_YEAR = new Date().getFullYear();
const ITEM_HEIGHT = 40;
const VISIBLE_ITEMS = 5;

// Helpers
const getDaysInMonth = (month: number, year: number) => new Date(year, month + 1, 0).getDate();
const getYears = () => Array.from({ length: MAX_YEAR - MIN_YEAR + 1 }, (_, i) => (MAX_YEAR - i).toString());

// Picker Column Component
const PickerColumn = memo(({ items, selectedIndex, onSelect, maxItems = 31 }: { items: string[]; selectedIndex: number; onSelect: (index: number) => void; maxItems?: number }) => {
  const scrollY = useSharedValue(0);
  const scrollRef = useRef<Animated.ScrollView>(null);

  useEffect(() => {
    scrollRef.current?.scrollTo({ y: selectedIndex * ITEM_HEIGHT, animated: false });
  }, [selectedIndex]);

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: e => {
      scrollY.value = e.contentOffset.y;
    },
    onMomentumEnd: e => {
      const index = Math.round(e.contentOffset.y / ITEM_HEIGHT);
      const clamped = Math.max(0, Math.min(items.length - 1, index));
      runOnJS(onSelect)(clamped);
    },
  });

  return (
    <View style={{ height: ITEM_HEIGHT * VISIBLE_ITEMS, overflow: 'hidden' }}>
      <AnimatedScrollView
        bounces={false}
        ref={scrollRef}
        showsVerticalScrollIndicator={false}
        snapToInterval={ITEM_HEIGHT}
        contentContainerStyle={styles.scrollContent}
        onScroll={scrollHandler}>
        {Array.from({ length: maxItems }).map((_, index) => {
          const animatedStyle = useAnimatedStyle(() => {
            const distance = Math.abs(scrollY.value - index * ITEM_HEIGHT);
            const opacity = Math.max(0.5, 1 - distance / (ITEM_HEIGHT * VISIBLE_ITEMS));
            const scale = Math.max(0.85, 1 - distance / (ITEM_HEIGHT * VISIBLE_ITEMS));
            return { opacity, transform: [{ scale }] };
          });

          const text = items[index] ?? ''; // blank if outside current days

          return (
            <Animated.View key={index} style={[styles.item, animatedStyle]}>
              <Text color="neutral70" fs="16" fw="600" ta="center">
                {text}
              </Text>
            </Animated.View>
          );
        })}
      </AnimatedScrollView>
      <View style={styles.highlight} pointerEvents="none" />
    </View>
  );
});

// Column Builders
const BuildDay = ({ selectedDay, month, year, onSelect }: { selectedDay: number; month: number; year: number; onSelect: (index: number) => void }) => {
  const days = useMemo(() => {
    const max = getDaysInMonth(month, year);
    return Array.from({ length: max }, (_, i) => (i + 1).toString());
  }, [month, year]);

  return (
    <View flex={1}>
      <Text ta="center" fw="700" fs="12" color="neutral80" mb={5}>
        DAY
      </Text>
      <PickerColumn items={days} selectedIndex={selectedDay} onSelect={onSelect} />
    </View>
  );
};

const BuildMonth = ({ selectedMonth, onSelect }: { selectedMonth: number; onSelect: (index: number) => void }) => (
  <View flex={1}>
    <Text ta="center" fw="700" fs="12" color="neutral80" mb={5}>
      MONTH
    </Text>
    <PickerColumn items={MONTHS} selectedIndex={selectedMonth} onSelect={onSelect} />
  </View>
);

const BuildYear = ({ years, selectedYear, onSelect }: { years: string[]; selectedYear: number; onSelect: (index: number) => void }) => (
  <View flex={1}>
    <Text ta="center" fw="700" fs="12" color="neutral80" mb={5}>
      YEAR
    </Text>
    <PickerColumn items={years} selectedIndex={selectedYear} onSelect={onSelect} />
  </View>
);

type DatePickerScreen = {
  onDateSelected?: (date: Date) => void;
  closeSheet?: () => void;
};
const DatePickerScreen: React.FC<DatePickerScreen> = ({ onDateSelected, closeSheet }) => {
  const years = getYears();
  const [yearIndex, setYearIndex] = useState(0); // 2025
  const [monthIndex, setMonthIndex] = useState(new Date().getMonth());
  const [dayIndex, setDayIndex] = useState(0); // Day 1

  const yearValue = MAX_YEAR - yearIndex;

  // Reset day index on month/year change
  useEffect(() => {
    const max = getDaysInMonth(monthIndex, MAX_YEAR - yearIndex);
    if (dayIndex >= max) setDayIndex(0);
  }, [monthIndex, yearIndex]);

  const handleSelect = () => {
    // First check if user is on selecting future dates
    const now = new Date();
    const selectedYear = MAX_YEAR - yearIndex;

    const isCurrentYear = selectedYear === now.getFullYear();
    const isFutureMonth = monthIndex > now.getMonth();
    const isSameMonthFutureDay = monthIndex === now.getMonth() && dayIndex + 1 > now.getDate();

    const isNotValid = isCurrentYear && (isFutureMonth || isSameMonthFutureDay);
    if (isNotValid) {
      notify.top('You can only pick dates up to today.');
      return;
    }
    const newDate = new Date(Number(years[yearIndex]), monthIndex, dayIndex + 1);
    if (!isNotValid) {
      onDateSelected?.(newDate);
      closeSheet?.();
    }
    closeSheet?.();
  };

  return (
    <View p={14} br={20} disableSizeMatter>
      <View fd="row" ai="center" jc="space-between">
        <Text color="neutral90" fw="700" fs="16">
          Select Date
        </Text>
        <Ripple onPress={handleSelect}>
          <View px={5} py={3}>
            <Text mx={5} color="positive50" fw="700" fs="14">
              CONFIRM
            </Text>
          </View>
        </Ripple>
      </View>
      <View mt={8} fd="row">
        <BuildDay selectedDay={dayIndex} month={monthIndex} year={yearValue} onSelect={setDayIndex} />
        <BuildMonth selectedMonth={monthIndex} onSelect={setMonthIndex} />
        <BuildYear years={years} selectedYear={yearIndex} onSelect={setYearIndex} />
      </View>
    </View>
  );
};

// Styles
const styles = StyleSheet.create({
  scrollContent: {
    paddingVertical: ITEM_HEIGHT * (VISIBLE_ITEMS / 2),
  },
  item: {
    height: ITEM_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
  },
  highlight: {
    position: 'absolute',
    top: '50%',
    height: ITEM_HEIGHT,
    left: 0,
    right: 0,
    borderTopWidth: 1.5,
    borderBottomWidth: 1.5,
    borderColor: 'rgba(49, 49, 50, 0.2)',
  },
});

interface DatePicker {
  children?: React.ReactElement;

  onDateSelected?: (date: Date) => void;

  initialDate?: Date;
}

export const DatePicker: React.FC<DatePicker> = ({ children, onDateSelected }) => {
  const { openSheet, Sheet, closeSheet } = useBottomSheet();
  return (
    <>
      {React.isValidElement(children) ? <Pressable onPress={openSheet}>{children}</Pressable> : <Button onPress={openSheet}>Open Sheet</Button>}
      <Sheet>
        <DatePickerScreen closeSheet={closeSheet} onDateSelected={onDateSelected} />
      </Sheet>
    </>
  );
};
