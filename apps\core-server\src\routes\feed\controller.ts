import { Infer } from "@vinejs/vine/types";
import type { Request, Response, NextFunction } from "express";
import {
  createPollValidator,
  createPostValidator,
  votePollValidator,
} from "./validator.js";
import { bodyValidator } from "@/lib/validation-module.js";
import { FeedRepo } from "@repo/repo";
import db from "@repo/db";

export class FeedController {
  @bodyValidator(createPostValidator)
  static async createPost(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof createPostValidator>;
      const output = await db.transaction(async (tx) => {
        return await FeedRepo.createFeedItem({
          tx,
          data: {
            type: "post",
            userId: req.user.id,
            body: payload.body,
            title: payload.title,
            disableComments: payload.disableComments,
          },
        });
      });
      res.json({
        data: output,
        message: "Post created successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  @bodyValidator(createPollValidator)
  static async createPoll(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof createPollValidator>;
      const output = await db.transaction(async (tx) => {
        return await FeedRepo.createFeedItem({
          tx,
          data: {
            type: "poll",
            userId: req.user.id,
            options: payload.options,
            title: payload.title,
            disableComments: payload.disableComments,
            multiSelect: payload.multiSelect,
          },
        });
      });
      res.json({
        data: output,
        message: "Poll created successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  static async likeFeedItem(req: Request, res: Response, next: NextFunction) {
    try {
      const feedItemId = req.params.id as string;

      const output = await FeedRepo.likeFeedItem({
        tx: db,
        data: {
          userId: req.user.id,
          feedItemId,
        },
      });

      res.json({ message: `Feed item ${output ? "liked" : "unliked"}` });
    } catch (error) {
      next(error);
    }
  }
  static async bookmarkFeedItem(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const feedItemId = req.params.id as string;

      const output = await FeedRepo.bookmarkFeedItem({
        tx: db,
        data: {
          userId: req.user.id,
          feedItemId,
        },
      });

      res.json({ message: output ? "Bookmarked" : "Removed from bookmarks" });
    } catch (error) {
      next(error);
    }
  }

  static async votePoll(req: Request, res: Response, next: NextFunction) {
    try {
      const pollId = req.params.id as string;
      const payload = req.body as Infer<typeof votePollValidator>;
      const output = await FeedRepo.selectPollOption({
        tx: db,
        data: {
          userId: req.user.id,
          pollOptionId: payload.optionId,
          pollId,
        },
      });
      res.json({ data: `Poll ${output ? "voted" : "unvoted"}` });
    } catch (error) {
      next(error);
    }
  }

  static async getFeed(req: Request, res: Response, next: NextFunction) {
    try {
      const query = req.query as Record<string, string>;
      const output = await FeedRepo.listFeedItems(query);

      res.json({ data: output });
    } catch (error) {
      next(error);
    }
  }
}
