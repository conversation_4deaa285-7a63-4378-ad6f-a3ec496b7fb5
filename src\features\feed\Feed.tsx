import { ThemeColors } from '@/types/color-types';
import CommentIcon from '@assets/svgs/comment-icon.svg';
import CreateButtonContainer from '@assets/svgs/create-feed-button-container.svg';
import HashtagIcon from '@assets/svgs/hashtag-icon.svg';
import HeartIconFilled from '@assets/svgs/heart-icon-filled.svg';
import HeartIcon from '@assets/svgs/heart-icon.svg';
import PlusIcon from '@assets/svgs/plus-icon.svg';
import PollIcon from '@assets/svgs/poll-icon.svg';
import SaveIconFilled from '@assets/svgs/save-icon-filled.svg';
import SaveIcon from '@assets/svgs/save-icon.svg';
import { Button, Pressable, Text, View } from '@components/native';
import { Avatar, FlashList, ScreenWrapper } from '@components/shared';
import { BounceTap, LoaderCentered } from '@components/shared/animated';
import { useBottomSheet } from '@components/shared/bottom-sheet/BottomSheet';
import { notify, useAuth, useTheme } from '@context/index';
import { BottomSheetFlashList } from '@gorhom/bottom-sheet';
import { router } from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import { FeedItem } from '@packages/useStitch/types/feed-api-types';
import SCREENS from '@src/SCREENS';
import React, { useCallback, useEffect, useState } from 'react';
import { Image } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';
import Feather from 'react-native-vector-icons/Feather';

interface Comment {
  username: string;
  avatar: any;
  timeAgo: string;
  content: string;
  replies: CommentReplies[];
}

interface CommentReplies {
  username: string;
  avatar: any;
  timeAgo: string;
  content: string;
  repliedTo: string;
}

// Mock comments for now - these would come from a separate API in a real implementation
const getMockComments = (): Comment[] => [
  {
    username: 'mountain_lover',
    avatar: require('@assets/placeholders/comment-avatar-1.png'),
    timeAgo: '5 mins ago',
    content: 'Wow, looks amazing! How long was the hike?',
    replies: [
      {
        username: 'travel_guru',
        avatar: require('@assets/placeholders/feed-avatar-1.png'),
        timeAgo: '3 mins ago',
        content: 'It took around 5 hours. The trail was steep but worth every step!',
        repliedTo: 'mountain_lover',
      },
    ],
  },
  {
    username: 'nature_fan',
    avatar: require('@assets/placeholders/comment-avatar-2.png'),
    timeAgo: '3 mins ago',
    content: 'The Alps are on my bucket list! Thanks for sharing.',
    replies: [],
  },
];

// Helper function to format time ago from createdAt timestamp
const formatTimeAgo = (createdAt: string): string => {
  const now = new Date();
  const date = new Date(createdAt);
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 604800)}w ago`;
  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)}mo ago`;

  return date.toLocaleDateString();
};

const CardHeader = React.memo(({ item, colors }: { item: FeedItem; colors: ThemeColors }) => {
  return (
    <View>
      <View fd="row" ai="center" mb={16} jc="space-between">
        <View fd="row" ai="center">
          <Avatar size={40} borderWidth={1.6} imageSource={item.user.avatar} />
          {/* <Image width={40} height={40} borderRadius={20} source={getAvatarSource(item.user.avatar)} style={{ marginRight: 12, borderWidth: 1.6, borderColor: colors.neutral10 }} /> */}
          <View ml={12}>
            <Text
              // ff="PoppinsRegular"
              fs="16"
              fw="700">
              @{item.user.username}
            </Text>
            <Text
              // ff="PoppinsRegular"
              fs="12"
              color="neutral60"
              mt={4}>
              {formatTimeAgo(item.createdAt)}
            </Text>
          </View>
        </View>
        <Pressable onPress={() => {}}>
          <Feather name="more-vertical" size={18} color={colors.foreground} />
        </Pressable>
      </View>

      <Text
        // ff="PoppinsRegular"
        fw="700"
        fs="18">
        {item.title}
      </Text>
    </View>
  );
});

const CardFooter = ({ item, onOpenSheet }: { item: FeedItem; onOpenSheet: (comments: Comment[]) => void }) => {
  const [liked, setLiked] = useState(item.liked);
  const [saved, setSaved] = useState(item.bookmarked);
  const [likeCount, setLikeCount] = useState(item.metrics.totalLikes);

  const { mutate: likeMutate, isMutating: isLiking } = useStitch('likeFeedItem', {
    queryParams: { id: item.id },
    mutationOptions: {
      onError: error => {
        handleLike();
        notify.top('Failed to update like status');
        console.error('Like error:', error);
      },
    },
  });

  const { mutate: bookmarkMutate, isMutating: isBookmarking } = useStitch('bookmarkFeedItem', {
    queryParams: { id: item.id },
    mutationOptions: {
      onError: error => {
        handleBookmark();
        notify.top('Failed to update bookmark status');
        console.error('Bookmark error:', error);
      },
    },
  });

  const handleLike = (buttonPress = false) => {
    setLiked(!liked);
    setLikeCount(liked ? likeCount - 1 : likeCount + 1);
    if (buttonPress && !isLiking) {
      likeMutate(undefined);
    }
  };

  const handleBookmark = (buttonPress = false) => {
    setSaved(!saved);
    if (buttonPress && !isBookmarking) {
      bookmarkMutate(undefined);
    }
  };

  return (
    <View>
      <View mt={16} fd="row" ai="center" jc="space-between">
        <View fd="row" ai="center">
          <Pressable onPress={() => handleLike(true)} disabled={isLiking}>
            {liked ? <HeartIconFilled width={24} height={24} style={{ marginRight: 6 }} /> : <HeartIcon width={24} height={24} style={{ marginRight: 6 }} />}
          </Pressable>
          <Text mr={16} fs="14">
            {likeCount}
          </Text>
          {item.disableComments !== true && (
            <Pressable onPress={() => onOpenSheet(getMockComments())} style={{ flexDirection: 'row', alignItems: 'center' }}>
              <CommentIcon width={24} height={24} style={{ marginRight: 6 }} />
              <Text fs="14">{item.metrics.totalComments}</Text>
            </Pressable>
          )}
        </View>
        {item.type === 'post' && (
          <View>
            <Pressable onPress={() => handleBookmark(true)} disabled={isBookmarking}>
              {saved ? <SaveIconFilled width={24} height={24} /> : <SaveIcon width={24} height={24} />}
            </Pressable>
          </View>
        )}
      </View>
    </View>
  );
};

const PostCard = React.memo(({ post, onOpenSheet }: { post: FeedItem; onOpenSheet: (comments: Comment[]) => void }) => {
  const { colors } = useTheme();
  return (
    <View px={12} pb={24}>
      <View bg="white" br={20} bw={1} bc="purpleLight">
        <View px={13} py={16}>
          <CardHeader item={post} colors={colors} />
          <Text
            // ff="PoppinsRegular"
            mt={8}>
            {post.body}
          </Text>
          <CardFooter item={post} onOpenSheet={onOpenSheet} />
        </View>
      </View>
    </View>
  );
});

const PollCard = React.memo(({ poll, onOpenSheet }: { poll: FeedItem; onOpenSheet: (comments: Comment[]) => void }) => {
  if (!poll.poll) return null;
  const { colors } = useTheme();
  const [pollOptions, setPollOptions] = useState(poll.poll);
  const [hasVoted, setHasVoted] = useState(poll.voted || false); // Track if user has voted
  const widthAnimations = poll.poll.map(() => useSharedValue(0));
  const springConfig = { damping: 100, stiffness: 150, mass: 1 };
  const [previousPollState, setPreviousPollState] = useState<typeof pollOptions | null>(null);

  const animateBars: (options: typeof pollOptions) => void = options => {
    const totalVotes = options.reduce((a: number, b) => a + b.votes, 0);
    options.forEach((_, i) => {
      const percentage = totalVotes > 0 ? Math.round((options[i].votes / totalVotes) * 100) : 0;
      widthAnimations[i].value = withSpring(percentage, springConfig);
    });
  };

  const onSuccess = () => {
    setPreviousPollState(null);
  };

  const onError = (error: any) => {
    notify.top('Failed to update vote');
    console.error('Update vote error:', error);
    if (previousPollState) {
      setPollOptions(previousPollState);
      setHasVoted(previousPollState.some(opt => opt.voted));
      animateBars(previousPollState);
      setPreviousPollState(null);
    }
  };

  const { mutate: voteMutate, isMutating: isVoting } = useStitch('votePoll', {
    queryParams: { id: poll.id },
    mutationOptions: {
      onSuccess,
      onError,
    },
  });

  // const { mutate: removeVotesMutate, isMutating: isRemovingVotes } = useStitch('removeVotes', {
  //   queryParams: { id: poll.id },
  //   mutationOptions: {
  //     onSuccess,
  //     onError,
  //   },
  // });

  useEffect(() => {
    if (hasVoted) {
      animateBars(pollOptions);
    } else {
      pollOptions.forEach((_, i) => {
        widthAnimations[i].value = withSpring(0, springConfig);
      });
    }
  }, [hasVoted, pollOptions]);

  const handleVote = (index: number) => {
    if (isVoting) return;
    const option = pollOptions[index];
    const isMultiSelect = poll.multiSelect || false;
    if (!isMultiSelect && hasVoted) return;
    setPreviousPollState([...pollOptions]);
    const newPollOptions = [...pollOptions];

    if (isMultiSelect && option.voted) {
      newPollOptions[index].voted = false;
      newPollOptions[index].votes = Math.max(0, newPollOptions[index].votes - 1);
    } else {
      if (!isMultiSelect) {
        newPollOptions.forEach((opt, i) => {
          if (i !== index && opt.voted) {
            opt.voted = false;
            opt.votes = Math.max(0, opt.votes - 1);
          }
        });
      }
      newPollOptions[index].voted = true;
      newPollOptions[index].votes += 1;
    }

    setPollOptions(newPollOptions);
    setHasVoted(newPollOptions.some(opt => opt.voted));
    animateBars(newPollOptions);
    voteMutate({ optionId: option.id });
  };

  const handleRemoveVotes = () => {
    if (isVoting) return;
    const votedOptions = pollOptions.filter(option => option.voted);
    if (votedOptions.length === 0) return;
    setPreviousPollState([...pollOptions]);
    const newPollOptions = [...pollOptions];

    // update this section later to remove all votes instead of first
    // newPollOptions.forEach(option => {
    //   if (option.voted) {
    //     option.voted = false;
    //     option.votes = Math.max(0, option.votes - 1);
    //   }
    // });
    const votedOptionIndex = newPollOptions.findIndex(option => option.voted);
    if (votedOptionIndex !== -1) {
      newPollOptions[votedOptionIndex].voted = false;
      newPollOptions[votedOptionIndex].votes = Math.max(0, newPollOptions[votedOptionIndex].votes - 1);
    }

    setPollOptions(newPollOptions);
    // update this section later to remove all votes instead of first
    // setHasVoted(false);
    setHasVoted(newPollOptions.some(opt => opt.voted));
    animateBars(newPollOptions);
    // update this section later to call
    voteMutate({ optionId: votedOptions[0].id });
  };

  const totalVotes = pollOptions.reduce((a: number, b) => a + b.votes, 0);
  const getVotePercentage = (index: number) => (totalVotes > 0 ? Math.round((pollOptions[index].votes / totalVotes) * 100) : 0);

  // Create animated styles for each poll option
  const createAnimatedBarStyle = (index: number) =>
    useAnimatedStyle(() => ({
      width: `${widthAnimations[index].value}%`,
    }));

  return (
    <View px={8} pb={24}>
      <View bg="white" br={20} bw={1} bc="purpleLight">
        <View px={13} py={16}>
          <CardHeader item={poll} colors={colors} />
          {pollOptions.map((option, index: number) => {
            const animatedBarStyle = createAnimatedBarStyle(index);
            return (
              <Pressable key={index} onPress={() => handleVote(index)} disabled={isVoting}>
                <View bg="purple100" jc="center" h={48} br={10} mt={12}>
                  <Animated.View
                    style={[
                      {
                        backgroundColor: colors.purple300,
                        height: '100%',
                        borderRadius: 10,
                        position: 'absolute',
                        left: 0,
                      },
                      animatedBarStyle,
                    ]}
                  />
                  <View fd="row" ai="center" jc="space-between" px={21}>
                    <View flex={1} mr={4}>
                      <Text key={'option-' + index} fw="500" fs="16" numberOfLines={1}>
                        {option.option}
                      </Text>
                    </View>
                    {hasVoted && (
                      <View fd="row" ai="center">
                        {option.voted && <Avatar size={20} showProgressRing={false} />}
                        <View pl={4}>
                          <Text fw="700" fs="16" color="neutral80">
                            {getVotePercentage(index)}%
                          </Text>
                        </View>
                      </View>
                    )}
                  </View>
                </View>
              </Pressable>
            );
          })}
          <View h={23}>
            {hasVoted && (
              <View fd="row" ai="center" jc="space-between" pos="absolute" style={{ bottom: 0, left: 0, right: 0 }}>
                <Text fw="500" fs="12" color="neutral40">
                  {totalVotes} response{totalVotes === 1 ? '' : 's'}
                </Text>
                <Pressable onPress={handleRemoveVotes}>
                  <Text fw="600" fs="12" color="neutral50">
                    Remove vote{poll.multiSelect ? 's' : ''}?
                  </Text>
                </Pressable>
              </View>
            )}
          </View>
          <CardFooter item={poll} onOpenSheet={onOpenSheet} />
        </View>
      </View>
    </View>
  );
});

const CreateButton = React.memo(({ onSuccess }: { onSuccess: () => void }) => {
  const { colors } = useTheme();
  const [showView, setShowView] = useState(false);
  return (
    <View pos="absolute" bottom={60} z={20} right={24}>
      <View bottom={-4} z={20} right={-4.5}>
        {showView && (
          <View>
            <CreateButtonContainer color={colors.background} />
            <View pos="absolute" px={10}>
              <Pressable
                onPress={() => {
                  setShowView(false);
                  router.navigate(SCREENS.CREATE_NEW_POST, { onSuccess });
                }}>
                <View fd="row" py={10}>
                  <HashtagIcon width={24} height={24} style={{ marginRight: 6 }} />
                  <Text fw="500" fs="14" color="purple500">
                    Add post
                  </Text>
                </View>
              </Pressable>
              <View h={1} w={'100%'} bg="neutral20" />
              <Pressable
                onPress={() => {
                  setShowView(false);
                  router.navigate(SCREENS.CREATE_NEW_POLL, { onSuccess });
                }}>
                <View fd="row" py={10}>
                  <PollIcon width={24} height={24} style={{ marginRight: 6 }} />
                  <Text fw="500" fs="14" color="purple500">
                    Add poll
                  </Text>
                </View>
              </Pressable>
            </View>
          </View>
        )}
      </View>
      <View pos="absolute" bottom={0} z={20} right={0}>
        <BounceTap onPress={() => setShowView(!showView)}>
          <View size={50} flexCenterRow bg={showView ? 'neutral30' : 'purple500'} br={100} bw={1} bc="purpleLight">
            <PlusIcon fill={colors.background} style={{ transform: [{ rotate: showView ? '45deg' : '0deg' }] }} />
          </View>
        </BounceTap>
      </View>
    </View>
  );
});

const CommentSheet = ({ comments, Sheet, closeSheet }: { comments: Comment[]; Sheet: React.FC<any>; closeSheet: () => void }) => {
  const [expandedReplies, setExpandedReplies] = useState<number[]>([]);

  const handleToggleReplies = React.useCallback((index: number) => {
    setExpandedReplies(prev => {
      if (prev.includes(index)) {
        console.log('hiding replies for:', index);
        return prev.filter(i => i !== index);
      } else {
        console.log('showing replies for:', index);
        return [...prev, index];
      }
    });
  }, []);

  const renderCommentItem = React.useCallback(
    ({ item: comment, index }: { item: Comment; index: number }) => {
      const showReplies = expandedReplies.includes(index);
      return (
        <View key={'comment-' + index} mb={18}>
          <View fd="row" ai="center" mb={0}>
            <Image width={36} height={36} borderRadius={18} source={comment.avatar} />
            <View w={6} />
            <Text fw="400" fs="14" color="neutral80">
              {comment.username}
            </Text>
            <View flex={1} />
            <Text color="neutral50" fs="10">
              {comment.timeAgo}
            </Text>
          </View>
          <View fd="row" ai="flex-start">
            <View w={36} />
            <View flex={1}>
              <Text fs="10" color="neutral70" mt={2}>
                {comment.content}
              </Text>
              <View h={8} />
              <View fd="row" ai="center">
                <Text ff="Montserrat-Regular" fs="12" color="neutral70" fw="700">
                  Reply
                </Text>
                {comment.replies.length > 0 && (
                  <>
                    <View w={2} h={2} mx={5} bg="neutral80" br={100} />
                    <Pressable onPress={() => handleToggleReplies(index)}>
                      {showReplies ? (
                        <Text fw="400" fs="10" color="neutral50">
                          Hide {comment.replies.length === 1 ? 'reply' : 'replies'}
                        </Text>
                      ) : (
                        <Text fw="400" fs="10" color="neutral50">
                          Show {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}
                        </Text>
                      )}
                    </Pressable>
                  </>
                )}
              </View>
              {showReplies && (
                <View fd="row">
                  <View w={3} bg="purpleLight" />
                  <BottomSheetFlashList
                    data={comment.replies}
                    estimatedItemSize={30}
                    contentContainerStyle={{ paddingTop: 8, paddingLeft: 8, backgroundColor: 'background' }}
                    renderItem={({ item: reply, index: rIndex }) => (
                      <View key={'reply-' + rIndex} mb={8}>
                        <View fd="row" ai="center" mb={0}>
                          <Image width={36} height={36} borderRadius={18} source={reply.avatar} />
                          <View w={6} />
                          <Text fw="400" fs="14" color="neutral80">
                            {reply.username}
                          </Text>
                          <View flex={1} />
                          <Text color="neutral50" fs="10">
                            {reply.timeAgo}
                          </Text>
                        </View>
                        <Text fs="10" color="neutral70" fw="700">
                          @{reply.repliedTo}
                        </Text>
                        <View fd="row" ai="flex-start">
                          <View flex={1}>
                            <Text fs="10" color="neutral70" mt={2}>
                              {reply.content}
                            </Text>
                          </View>
                        </View>
                        <View h={8} />
                        <View fd="row" ai="center">
                          <Text fs="12" color="neutral70" fw="700">
                            Reply
                          </Text>
                        </View>
                      </View>
                    )}
                  />
                  {/* </View> */}
                </View>
              )}
            </View>
          </View>
        </View>
      );
    },
    [expandedReplies, handleToggleReplies],
  );

  return (
    <Sheet withBackButton={false} enablePanDownToClose={true} onDismiss={closeSheet}>
      <BottomSheetFlashList
        data={comments}
        estimatedItemSize={30}
        ListHeaderComponent={
          <View pt={25} pb={10}>
            <Text fs="24" fw="600" ta="left" color="neutral80">
              Comments
            </Text>
          </View>
        }
        contentContainerStyle={{ paddingHorizontal: 20 }}
        renderItem={renderCommentItem}
        extraData={expandedReplies}
      />
    </Sheet>
  );
};

export const FeedScreen = () => {
  const profilePercentage = useAuth().session!.user!.profilePercentage ?? 0;
  const { data: feedResponse, isInitialLoading, isPaginatedLoading, isLoading, isInitialError, fetchNextPage, refetch: refetchFeed } = useStitch('getFeed');
  const { Sheet, openSheet, closeSheet } = useBottomSheet({ snapPoints: ['100%'] });
  const [comments, setComments] = useState<Comment[]>([]);

  const onOpenSheet = useCallback((comments: Comment[]) => {
    setComments(comments);
  }, []);

  useEffect(() => {
    if (comments !== undefined && comments.length > 0) {
      openSheet();
    }
  }, [comments, openSheet]);

  if (isInitialLoading) {
    return (
      <ScreenWrapper hideHeader>
        <LoaderCentered />
      </ScreenWrapper>
    );
  }

  if (isInitialError) {
    return (
      <ScreenWrapper hideHeader>
        <View flex={1} jc="center" ai="center" px={20}>
          <Text fs="18" fw="600" mb={16} ta="center">
            Failed to load feed
          </Text>
          <Text fs="14" color="neutral60" mb={24} ta="center">
            Something went wrong while loading your feed. Please try again.
          </Text>
          <Button onPress={refetchFeed}>Retry</Button>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper hideHeader>
      <CreateButton onSuccess={refetchFeed} />
      <FlashList
        hideHeader
        data={feedResponse?.data || []}
        onRefresh={refetchFeed}
        refreshing={isLoading || isPaginatedLoading}
        estimatedItemSize={300}
        onEndReached={fetchNextPage}
        onEndReachedThreshold={0.5}
        ListFooterComponent={() => (
          <View h={15}>
            {isPaginatedLoading && (
              <View py={20}>
                <LoaderCentered />
              </View>
            )}
          </View>
        )}
        ListHeaderComponent={
          <View px={12} pb={24}>
            {profilePercentage < 100 && (
              <View mt={11}>
                <View mih={200} bg="lightBlue" br={20} bw={1} bc="purpleLight" px={13} pb={13} pt={23}>
                  <View fd="row" ai="flex-start">
                    <View>
                      <Text ff="PlayfairDisplay-Regular" fw="700" fs="20" color="purple700">
                        Let's Personalize{'\n'}Your Journey 💫
                      </Text>
                      <Text mt={19} fw="400" fs="12" color="neutral60">
                        A few more details will help us find{'\n'}people who truly get you.
                      </Text>
                    </View>
                  </View>
                  <View fd="row" jc="space-between" mt={15} mb={25}>
                    <View flex={1} bg="background" br={20} bw={1} bc="neutral10" h={10}>
                      <View flex={1} w={`${profilePercentage}%`} bg="purple300" br={20} />
                    </View>
                    <Text
                      // ff='PoppinsRegular'
                      fw="400"
                      fs="10"
                      color="neutral70"
                      ml={9}>
                      {profilePercentage}% complete{profilePercentage > 60 ? ', Almost there...' : ''}
                    </Text>
                  </View>
                  <Button isFullWidth={true} onPress={() => {}}>
                    Update profile
                  </Button>
                </View>
                <View pos="absolute" top={-30} right={0}>
                  <Image source={require('@assets/images/complete-profile.png')} />
                </View>
              </View>
            )}
          </View>
        }
        renderItem={({ item }: { item: FeedItem }) => {
          if (item.type === 'post') {
            return <PostCard post={item} onOpenSheet={onOpenSheet} />;
          } else if (item.type === 'poll') {
            return <PollCard poll={item} onOpenSheet={onOpenSheet} />;
          } else {
            return null;
          }
        }}
      />
      <CommentSheet
        comments={comments}
        Sheet={Sheet}
        closeSheet={() => {
          closeSheet();
          setComments([]);
        }}
      />
    </ScreenWrapper>
  );
};
