import { CACHE_APIS } from '../api-base-cache-keys';
import { ApiResponse, QueryApiConfig } from '../types';
import { BadgeItem } from '../types/badge-api-types';

export namespace BadgeApis {
  export const badgesList = 'badgesList' as const;
}

const C = CACHE_APIS.badges;

export const badgeApiConfig = {
  badgesList: {
    path: '/chat/badges/list',
    method: 'GET',
    protected: true,
    responseType: undefined as unknown as ApiResponse<BadgeItem[]>,
    baseCacheKey: C.badgesList,
    staleTime: 30 * 60 * 1000, // 30 minutes
    type: 'query' as const,
  } satisfies QueryApiConfig<BadgeItem[]>,
};
