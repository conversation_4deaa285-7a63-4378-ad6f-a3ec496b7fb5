import FeatherIcon from '@assets/svgs/feather-icon.svg';
import PlusIcon from '@assets/svgs/plus-icon.svg';
import { Pressable, Text, View } from '@components/native';
import { Image } from '@components/native/Image';
import { AnimatedView, BounceTap, LoaderCentered } from '@components/shared/animated';
import { JournalFeedSkeleton } from '@components/shared/animated/skeletons/JournalSkeleton';
import { useTheme } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import { JournalCategoryItem, JournalFeedItem } from '@packages/useStitch/types';
import { format, parseISO } from 'date-fns';
import React, { FC, useRef, useState } from 'react';
import { Dimensions, FlatList } from 'react-native';
import Animated, { Extrapolation, SharedValue, interpolate, runOnJS, useAnimatedReaction, useAnimatedScrollHandler, useAnimatedStyle, useSharedValue } from 'react-native-reanimated';
import { NavigationState, SceneRendererProps, TabBar, TabDescriptor, TabView } from 'react-native-tab-view';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { useJournalStore } from './store/journal-store';
import { FlashList } from '@components/shared';

const { width } = Dimensions.get('screen');

const CarouselItem: React.FC<{
  item: JournalCategoryItem;
  index: number;
  scrollX: SharedValue<number>;
  cardWidth: number;
  spacing: number;
}> = ({ item, index, scrollX, cardWidth, spacing }) => {
  const animatedStyle = useAnimatedStyle(() => {
    const inputRange = [(index - 1) * (cardWidth + spacing), index * (cardWidth + spacing), (index + 1) * (cardWidth + spacing)];
    return {
      transform: [
        {
          scale: interpolate(scrollX.value, inputRange, [0.9, 1.1, 0.9], Extrapolation.CLAMP),
        },
      ],
      width: cardWidth + 1.2,
      marginHorizontal: spacing / 2,
    };
  });

  return (
    <AnimatedView style={[animatedStyle]}>
      <Image pos="absolute" objectFit="cover" top={0} left={0} h="100%" w="100%" btr={20} bbr={20} source={{ uri: item.bgImage }} />
      <View
        style={{
          height: cardWidth * 1.3,
        }}>
        <Text ml={30} mt={30} fw="600" fs="18" ff="PlayfairDisplay-SemiBold" color="black">
          {item.title}
        </Text>
        <View
          bg="purple100"
          bc="purple500"
          bw={1}
          br={100}
          display="flex"
          fd="row"
          ai="center"
          gap={5}
          px={5}
          py={4}
          style={{
            marginLeft: 30,
            marginTop: 5,
            alignSelf: 'flex-start',
          }}>
          <FeatherIcon />
          <Text color="purple700" fs="10">
            {item.totalCount} Entries
          </Text>
        </View>
      </View>
    </AnimatedView>
  );
};

interface RenderSwipableTabScreens extends SceneRendererProps {
  route: {
    key: string;
    title: string;
  };
}

/**
 * A component that renders swipeable tab screens and fetches journal feed data based on the route's category.
 * When the route title is "all" (case-insensitive), no query parameters are sent.
 * Otherwise, the route's key is used as the category query parameter.
 * @param props - Props including the route and SceneRendererProps.
 * @returns A React component rendering the tab screen content.
 */
const RenderSwipableTabScreens: React.FC<RenderSwipableTabScreens> = ({ route }) => {
  const { journalSearchQuery } = useJournalStore();
  const queryParams: Record<string, string> = {
    ...(route.title.toLowerCase() !== 'all' && { category: route.key }),
    ...(journalSearchQuery?.trim() && { q: journalSearchQuery.trim() }),
  };
  const { data, isError, isInitialLoading } = useStitch('journalFeedListByCategory', { queryParams });

  if (isInitialLoading) return <LoaderCentered />;
  if (isError) return <Text fs="12">Error loading data</Text>;
  if (data?.data.length == 0)
    return (
      <Text ta="center" my={20} fs="12">
        {journalSearchQuery.trim() ? 'No journal found , try searching with other keywords' : "You have'nt created journals for this category"}
      </Text>
    );
  return (
    <FlashList
      hideHeader
      ItemSeparatorComponent={() => <View style={{ height: 10 }} />}
      data={data?.data}
      contentContainerStyle={{ paddingHorizontal: 10, paddingTop: 10, paddingBottom: 60 }}
      renderItem={({ item, index }: { item: JournalFeedItem; index: number }) => {
        const date = parseISO(item.createdAt);
        const formattedDate = format(date, 'dd/MMM , yyyy').split('/');

        return (
          <Pressable
            onPress={() => {
              router.navigate('JournalDetails', {
                journalId: item.id,
                type: `${item.category.title} Journal`,
                publishedOn: formattedDate[0] + ' ' + formattedDate[1],
              });
            }}
            key={index}>
            <View p={10}>
              <View display="flex" ai="center" fd="row">
                <Text fw="600" fs="16">
                  {formattedDate[0]}{' '}
                </Text>
                <Text fs="12">{formattedDate[1]}</Text>
              </View>
              <View display="flex" fd="row" ai="center" gap={5}>
                <View size={60} mt={10} br={10}>
                  <Image size={60} objectFit="cover" source={{ uri: item.category.bgImage }} />
                </View>
                <View p={8} disableSizeMatter w={width - 100}>
                  <Text color="neutral80" fw="500" numberOfLines={1}>
                    {item.title}
                  </Text>
                  <Text mt={5} numberOfLines={2} fs="14">
                    {item.summary}
                  </Text>
                </View>
              </View>
            </View>
          </Pressable>
        );
      }}
      estimatedItemSize={110}
    />
  );
};
interface CustomTabBar extends SceneRendererProps {
  navigationState: NavigationState<{
    key: string;
    title: string;
  }>;
  options:
    | Record<
        string,
        TabDescriptor<{
          key: string;
          title: string;
        }>
      >
    | undefined;
  data: JournalCategoryItem[];
}
const CustomTabBar: FC<CustomTabBar> = ({ data = [], ...props }) => {
  const { colors } = useTheme();

  return (
    <TabBar
      bounces={false}
      tabStyle={{ width: 'auto', minWidth: width / data.length, paddingHorizontal: 10 }}
      indicatorContainerStyle={{ minWidth: width, backgroundColor: colors.background, borderTopLeftRadius: 20, borderTopRightRadius: 20 }}
      activeColor={colors.neutral80}
      inactiveColor={colors.neutral60}
      scrollEnabled={true}
      {...props}
      indicatorStyle={{
        height: 4,
        backgroundColor: colors.purple500,
      }}
      style={{
        backgroundColor: colors.orange,
        borderBottomColor: colors.neutral20,
        borderBottomWidth: 1,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
      }}
    />
  );
};

export const JournalScreen: React.FC = () => {
  const { journalSearchQuery, setJournalSearchQuery } = useJournalStore();
  const queryParams: Record<string, string> = {
    ...(journalSearchQuery?.trim() && { q: journalSearchQuery.trim() }),
  };
  const { data, isLoading } = useStitch('journalCategoryList', {
    queryParams,
  });
  const { colors, isDarkMode } = useTheme();
  const [index, setIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const scrollX = useSharedValue(0);
  const isTransitionOngoing = useSharedValue(false);

  const CARD_WIDTH = width * 0.45;
  const SPACING = 25;
  const SIDE_SPACER = (width - CARD_WIDTH) / 2;

  // Sync carousel scroll with TabView index
  useAnimatedReaction(
    () => ({ scrollX: scrollX.value, isTransitionOngoing: isTransitionOngoing.value }),
    ({ scrollX, isTransitionOngoing }) => {
      if (isTransitionOngoing) return;
      const newIndex = Math.round(scrollX / (CARD_WIDTH + SPACING));
      if (newIndex !== index && newIndex >= 0 && newIndex < (data?.data.length ?? [].length)) {
        runOnJS(setIndex)(newIndex);
      }
    },
    [index, CARD_WIDTH, SPACING],
  );

  // Handle TabView index change to scroll carousel
  const handleIndexChange = (newIndex: number) => {
    if (newIndex === index) return;
    isTransitionOngoing.value = true;
    setIndex(newIndex);
    if (flatListRef.current) {
      flatListRef.current.scrollToIndex({
        index: newIndex,
        animated: Math.abs(newIndex - index) <= 2,
      });
    }
    // Dynamic timeout based on index jump distance
    const jumpDistance = Math.abs(newIndex - index);
    const timeoutDuration = Math.max(300, jumpDistance * 50); // 50ms per index
    setTimeout(() => {
      isTransitionOngoing.value = false;
    }, timeoutDuration);
  };

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: event => {
      scrollX.value = event.contentOffset.x;
    },
    onBeginDrag: () => {
      isTransitionOngoing.value = true;
    },
    onMomentumEnd: () => {
      isTransitionOngoing.value = false;
    },
  });

  if (isLoading) {
    return <JournalFeedSkeleton />;
  }

  return (
    <View flex={1} style={{ backgroundColor: isDarkMode ? '#081012' : colors.orange }}>
      <View pos="absolute" bottom={60} z={20} right={24}>
        <BounceTap onPress={() => router.push('AddJournal')}>
          <View size={50} flexCenterRow bg="purple500" br={100} bw={1} bc="purpleLight">
            <PlusIcon fill="white" />
          </View>
        </BounceTap>
      </View>
      <View style={{ backgroundColor: isDarkMode ? '#081012' : colors.orange }} pb={25}>
        <View style={{ padding: 20 }}>
          <Pressable onPress={() => router.navigate('JournalSearch')}>
            <View bg="white" display="flex" fd="row" ai="center" jc="space-between" bc="neutral20" bw={1} br={16} pl={20} pr={10} py={14}>
              <Text color={journalSearchQuery.trim() ? 'neutral80' : 'neutral40'} fs="14">
                {journalSearchQuery.trim() ? journalSearchQuery : 'Search'}
              </Text>
              {journalSearchQuery.trim() && (
                <Pressable onPress={() => setJournalSearchQuery('')}>
                  <EvilIcons color={colors.neutral50} size={24} name="close" />
                </Pressable>
              )}
            </View>
          </Pressable>
        </View>
        <Animated.FlatList
          ref={flatListRef}
          pagingEnabled
          data={data?.data}
          renderItem={({ item, index }) => <CarouselItem item={item} index={index} scrollX={scrollX} cardWidth={CARD_WIDTH} spacing={SPACING} />}
          keyExtractor={item => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          snapToInterval={CARD_WIDTH + SPACING}
          decelerationRate={-1}
          bounces={false}
          contentContainerStyle={{ paddingHorizontal: SIDE_SPACER }}
          onScroll={scrollHandler}
          scrollEventThrottle={16}
          getItemLayout={(data, index) => ({
            length: CARD_WIDTH + SPACING,
            offset: (CARD_WIDTH + SPACING) * index,
            index,
          })}
        />
      </View>
      {!isLoading && (data?.data.length ?? [].length) > 0 && (
        <TabView
          navigationState={{ index, routes: (data?.data ?? []).map((item: JournalCategoryItem) => ({ key: item.id, title: item.title })) }}
          renderScene={(props: any) => <RenderSwipableTabScreens {...props} />}
          onIndexChange={handleIndexChange}
          initialLayout={{ width }}
          style={{ backgroundColor: colors.background, borderRadius: 20 }}
          renderTabBar={props => <CustomTabBar data={data?.data ?? []} {...props} />}
        />
      )}
    </View>
  );
};
