import { ENV } from '@config/env';
import { NavigationState } from '@react-navigation/native';
import SCREENS from '@src/SCREENS';
import { createContext, useContext, useEffect, useRef, useState } from 'react';
import { memAccessToken, useAuth } from '..';
import { SocketChatMessage } from './SocketMessageTypes';

type SocketConnnectionContext = {
  onNavigationStateChange: (state: Readonly<NavigationState> | undefined) => void;
  sendMessage: (message: any) => void;
  isConnected: boolean;
};

const SocketConnnectionContext = createContext<SocketConnnectionContext | undefined>(undefined);

const connectToWebSocket = (url: string, onMessageReceived: (message: SocketChatMessage) => void, onConnectionChange: (connected: boolean) => void) => {
  const ws = new WebSocket(url);

  ws.onopen = () => {
    console.log('WebSocket connected');
    onConnectionChange(true);
  };

  ws.onmessage = event => {
    try {
      const message = JSON.parse(event.data) as SocketChatMessage;
      onMessageReceived(message);
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  };

  ws.onerror = error => {
    console.error('WebSocket error:', error);
    onConnectionChange(false);
  };

  ws.onclose = () => {
    console.log('WebSocket disconnected');
    onConnectionChange(false);
  };

  return ws;
};

interface SocketConnectionProviderProps {
  children: React.ReactNode;
}
export const SocketConnectionProvider: React.FC<SocketConnectionProviderProps> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const { isAuthenticated, session } = useAuth();
  let SOCKET_URL = ENV.SOCKET_URL ?? 'ws://localhost:9001';
  SOCKET_URL = `${SOCKET_URL}?token=${memAccessToken}`;
  const [currentRoute, setCurrentRoute] = useState<string | null>(null);
  const ws = useRef<WebSocket>(null);
  const reconnectInterval = useRef(1000);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 0;

  const onNavigationStateChange = (state: Readonly<NavigationState> | undefined) => {
    if (!state) return;
    const currentRoute = state.routes[state.index];
    if (currentRoute.state?.type == 'drawer') {
      const drawerState = currentRoute.state.routes[currentRoute.state.index!];
      if (drawerState.name != 'tab') return;
      const tabState = drawerState.state?.routes[drawerState.state.index!];
      setCurrentRoute(tabState?.name ?? null);
    } else {
      setCurrentRoute(currentRoute.name);
    }
  };

  const onMessageReceived = (message: SocketChatMessage) => {
    console.log('Message Received >>', message);
  };

  useEffect(() => {
    if ((currentRoute == SCREENS.CHAT || currentRoute == SCREENS.CONVERSATIONS) && isAuthenticated) {
      // Connect to WebSocket
      if (!isConnected) {
        ws.current = connectToWebSocket(SOCKET_URL, onMessageReceived, setIsConnected);
      } else {
        console.log('Alreayd connected to socket no need for reconnection');
      }
    } else {
      // Disconnect if not on valid route
      if (ws.current && isConnected) {
        ws.current.close();
        ws.current = null;
        setIsConnected(false);
      }
    }
  }, [currentRoute, onMessageReceived, isAuthenticated]);

  // Reconnection logic
  useEffect(() => {
    if (!isConnected && ws.current && reconnectAttempts.current < maxReconnectAttempts && isAuthenticated) {
      const timeout = setTimeout(() => {
        reconnectAttempts.current += 1;
        reconnectInterval.current *= 2; // Exponential backoff
        ws.current = connectToWebSocket(SOCKET_URL, onMessageReceived, setIsConnected);
      }, reconnectInterval.current);
      return () => clearTimeout(timeout);
    }
  }, [isConnected, onMessageReceived]);

  const sendMessage = (message: any) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify(message));
    } else {
      console.error('WebSocket not connected');
    }
  };

  return (
    <SocketConnnectionContext.Provider
      value={{
        onNavigationStateChange,
        sendMessage,
        isConnected,
      }}>
      {children}
    </SocketConnnectionContext.Provider>
  );
};

export const useSocket = () => {
  const context = useContext(SocketConnnectionContext);
  if (!context) {
    throw new Error('useSocket must be used within a ThemeProvider');
  }
  return context;
};
