import { ChatMessage } from '@packages/useStitch/types';
import { createRef } from 'react';
import { FlatList } from 'react-native';
import uuid from 'react-native-uuid';
import { create } from 'zustand';

const mockMessages: ChatMessage[] = [
  {
    id: '1',
    message: 'Hi this is <PERSON> my name is Something john doe,,,,',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:00:00Z',
  },
  {
    id: '2',
    message: 'Hi my name is <PERSON><PERSON><PERSON>',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:02:00Z',
  },
  {
    id: '3',
    message: 'How are you doing?',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:03:00Z',
  },
  {
    id: '4',
    message: 'I’m doing great, thanks for asking!',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:04:00Z',
  },
  {
    id: '5',
    message: 'What’s up with you?',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:04:30Z',
  },
  {
    id: '6',
    message: 'Just chilling, how about you?',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:05:00Z',
  },
  {
    id: '7',
    message: 'Nice! I’m working on a project.',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:06:00Z',
  },
  {
    id: '8',
    message: 'Cool, what kind of project?',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:07:00Z',
  },
  {
    id: '9',
    message: 'It’s a mobile app for task management.',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:08:00Z',
  },
  {
    id: '10',
    message: 'That sounds awesome!',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:09:00Z',
  },
  {
    id: '11',
    message: 'Any cool features in it?',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:09:30Z',
  },
  {
    id: '12',
    message: 'Yeah, it has reminders and team collaboration.',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:10:00Z',
  },
  {
    id: '13',
    message: 'Sweet! Can I try it out sometime?',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:11:00Z',
  },
  {
    id: '14',
    message: 'Sure, I’ll send you a beta invite.',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:12:00Z',
  },
  {
    id: '15',
    message: 'Awesome, looking forward to it!',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:13:00Z',
  },
  {
    id: '16',
    message: 'By the way, you into gaming?',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:14:00Z',
  },
  {
    id: '17',
    message: 'Yeah, I play some FPS games.',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:15:00Z',
  },
  {
    id: '18',
    message: 'Which ones?',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:15:30Z',
  },
  {
    id: '19',
    message: 'I’m into Valorant and COD.',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:16:00Z',
  },
  {
    id: '20',
    message: 'Nice! We should play sometime.',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:17:00Z',
  },
  {
    id: '21',
    message: 'Definitely, add me on Discord?',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:18:00Z',
  },
  {
    id: '22',
    message: 'Sure, what’s your ID?',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:19:00Z',
  },
  {
    id: '23',
    message: 'It’s User#1234.',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:20:00Z',
  },
  {
    id: '24',
    message: 'Cool, I’ll add you tonight.',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:21:00Z',
  },
  {
    id: '25',
    message: 'Sounds good!',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:22:00Z',
  },
  {
    id: '26',
    message: 'Hey, you free this weekend?',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:23:00Z',
  },
  {
    id: '27',
    message: 'Yeah, mostly. What’s up?',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:24:00Z',
  },
  {
    id: '28',
    message: 'Thinking of a game night.',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:25:00Z',
  },
  {
    id: '29',
    message: 'Count me in!',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:26:00Z',
  },
  {
    id: '30',
    message: 'Awesome, I’ll set it up.',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:27:00Z',
  },
  {
    id: '31',
    message: 'Any snacks planned? 😄',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:28:00Z',
  },
  {
    id: '32',
    message: 'Pizza and chips, you in?',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:29:00Z',
  },
  {
    id: '33',
    message: 'Totally, love pizza!',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:30:00Z',
  },
  {
    id: '34',
    message: 'Cool, any favorite toppings?',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:31:00Z',
  },
  {
    id: '35',
    message: 'Pepperoni and extra cheese!',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:32:00Z',
  },
  {
    id: '36',
    message: 'Classic choice!',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:33:00Z',
  },
  {
    id: '37',
    message: 'What about you?',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:34:00Z',
  },
  {
    id: '38',
    message: 'I’m a veggie pizza fan.',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:35:00Z',
  },
  {
    id: '39',
    message: 'Nice, we’ll get both!',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:36:00Z',
  },
  {
    id: '40',
    message: 'Perfect plan.',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:37:00Z',
  },
  {
    id: '41',
    message: 'What time should we start?',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:38:00Z',
  },
  {
    id: '42',
    message: 'How about 7 PM?',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:39:00Z',
  },
  {
    id: '43',
    message: 'Works for me!',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:40:00Z',
  },
  {
    id: '44',
    message: 'Cool, I’ll set up the server.',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:41:00Z',
  },
  {
    id: '45',
    message: 'Sweet, can’t wait!',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:42:00Z',
  },
  {
    id: '46',
    message: 'Me neither!',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:43:00Z',
  },
  {
    id: '47',
    message: 'Hey, you got any game preferences ?',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:44:00Z',
  },
  {
    id: '48',
    message: 'Yeah, Valorant’s my go-to.',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:45:00Z',
  },
  {
    id: '49',
    message: 'Nice, we’ll have fun!',
    type: 'text',
    senderId: '5cd141d21b1d',
    sentAt: '2025-07-28T12:46:00Z',
  },
  {
    id: '50',
    message: 'For sure!',
    type: 'text',
    senderId: '180f3c48-db34-4854-9ec2-5cd141d21b1d',
    sentAt: '2025-07-28T12:47:00Z',
  },
];

interface useMessageStore {
  messageInputValue: string;
  setMessageInputValue: (value: string) => void;
  messages: ChatMessage[];
  flatListRef: React.RefObject<FlatList<any> | null>;
  onSendMessageClick: (value: string, userId: string) => Promise<void>;
}

export const useMessageStore = create<useMessageStore>((set, get) => {
  return {
    messageInputValue: '',
    setMessageInputValue: value => {
      set({ messageInputValue: value });
    },
    messages: [...mockMessages],
    flatListRef: createRef<FlatList>(),
    onSendMessageClick: async (value: string, userId: string) => {
      if (value == '') return;
      const { messages, flatListRef } = get();
      const newMessage = {
        id: uuid.v4(),
        message: value.trim(),
        senderId: userId,
        type: 'text',
      } as ChatMessage;

      set({ messages: [newMessage, ...messages] });
      set({ messageInputValue: '' });
      // Wait for UI to render, then scroll
      setTimeout(() => {
        flatListRef?.current?.scrollToOffset({ offset: 0, animated: true });
      }, 100); // 50–100ms is usually enough
    },
  };
});
