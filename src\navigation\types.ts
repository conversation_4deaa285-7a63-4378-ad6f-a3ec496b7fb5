import { ArticleData } from '@features/mood-tracker/Mood';
import { NavigationContainerRefWithCurrent, NavigationHelpers } from '@react-navigation/native';
import SCREENS from '@src/SCREENS';

type NavigationRef = NavigationContainerRefWithCurrent<RootNavigatorParamList>;

type NavigationRoot = NavigationHelpers<RootNavigatorParamList>;

/**
 * UnAuthenticated Params List
 * * Can be only accesible when user is not authenticated
 */
type UnAuthScreensParamsList = {
  [SCREENS.INTRO]: undefined;
  // [SCREENS.PRE_AUTH]: undefined;
  [SCREENS.SIGNUP]: undefined;
  [SCREENS.LOGIN]: undefined;
  [SCREENS.VERIFY_OTP]: {
    email: string;
    type: 'login' | 'signup';
  };
};

/**
 * 🔓 Public Params List
 * * Can be accessible from both private and public routes
 */
type SharedScreensParamsList = {
  [SCREENS.FAQS]: undefined;
  [SCREENS.PRIVACY_POLICY]: undefined;
  // [SCREENS.COUNTRY_LIST]: undefined;
  [SCREENS.TERMS_OF_SERVICE]: undefined;
};

/**
 *  ✅ Authenticated - Chat Routes
 *  * Feature wise params Lists.
 */
type ChatScreensParamsList = {
  [SCREENS.CONVERSATIONS]: undefined;
  [SCREENS.CHAT]: {
    firstName: string;
    userId: string;
    conversationId?: string;
    onGoBack?: (data?: any) => void;
  };
  [SCREENS.CHAT_SEARCH]: undefined;
};

/**
 *  ✅ Authenticated - Feed Routes
 *  * Feature wise params Lists.
 */
type FeedScreensParamsList = {
  [SCREENS.FEED]: undefined;
  [SCREENS.CREATE_NEW_POST]: { onSuccess?: () => void };
  [SCREENS.CREATE_NEW_POLL]: { onSuccess?: () => void };
  [SCREENS.EDIT_POST]: { postId: string };
  [SCREENS.EDIT_POLL]: { postId: string };
};

/**
 *  ✅ Authenticated - Journal Routes
 *  * Feature wise params Lists.
 */
type JournalScreensParamsList = {
  [SCREENS.JOURNAL]: undefined;
  [SCREENS.JOURNAL_SEARCH]: undefined;
  [SCREENS.JOURNAL_DETAILS]: { journalId: string; type: string; publishedOn: string };
  [SCREENS.CHOOSE_JOURNAL_TYPE]: undefined;
  [SCREENS.CREATE_JOURNAL_TYPE]: undefined;
  [SCREENS.ADD_JOURNAL]: undefined;
  [SCREENS.EDIT_JOURNAL]: { journalId: string; catId: string };
};

/**
 *  ✅ Authenticated - Mood tracker Routes
 *  * Feature wise params Lists.
 */
type MoodScreensParamsList = {
  [SCREENS.MOOD_TRACKER]: undefined;
  [SCREENS.MOOD_CALENDAR]: undefined;
  [SCREENS.MOOD_ARTICLE]: ArticleData;
};

/**
 *  ✅ Authenticated - Profile  Routes
 *  * Feature wise params Lists.
 */
type ProfileScreensParamsList = {
  [SCREENS.GET_STARTED]: undefined;
  [SCREENS.ONBOARDING]: undefined;
  [SCREENS.PROFILE]: undefined;
  [SCREENS.EDIT_PROFILE]: undefined;
  [SCREENS.SELECT_AVATAR]: undefined;
  [SCREENS.USERS_PROFILE]: { userId: string };
  [SCREENS.MY_FOLLOWERS]: undefined;
};

/**
 *  ✅ Authenticated - Drawer Screen  Routes
 *  * Feature wise params Lists.
 */
type DrawerScreensParamsList = {
  [SCREENS.BOOKMARKS]: undefined;
  [SCREENS.SETTINGS]: undefined;
  [SCREENS.BLOCKED_ACCOUNTS]: undefined;
};

export type RootNavigatorParamList = UnAuthScreensParamsList &
  SharedScreensParamsList &
  ChatScreensParamsList &
  FeedScreensParamsList &
  JournalScreensParamsList &
  MoodScreensParamsList &
  ProfileScreensParamsList &
  DrawerScreensParamsList;

export type {
  ChatScreensParamsList,
  DrawerScreensParamsList,
  FeedScreensParamsList,
  JournalScreensParamsList,
  MoodScreensParamsList,
  NavigationRef,
  NavigationRoot,
  ProfileScreensParamsList,
  SharedScreensParamsList,
  UnAuthScreensParamsList
};

