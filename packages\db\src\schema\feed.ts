import { aliasedTable, relations, sql } from "drizzle-orm";
import {
  pgTable,
  uuid,
  varchar,
  timestamp,
  boolean,
  date,
  pgEnum,
  text,
  numeric,
  integer,
  PgTransaction,
  json
} from "drizzle-orm/pg-core";
import { filesTable, users } from "./common.js";

export const feedTypeEnum = pgEnum("feed_type_enum", ["post", "poll"]);

export const feedItemTable = pgTable("feed_item", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  disableComments: boolean("disable_comments").default(false),
  type: feedTypeEnum("type").notNull().notNull(),
  deletedAt: timestamp("deleted_at")
});

export const postsTable = pgTable("posts_table", {
  id: uuid("id")
    .primaryKey()
    .references(() => feedItemTable.id),
  title: varchar("title", { length: 255 }).notNull(),
  body: text("body").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});

export const bookmarksTable = pgTable("bookmarks_table", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  feedItemId: uuid("feed_item_id")
    .notNull()
    .references(() => feedItemTable.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow(),
  deletedAt: timestamp("deleted_at"),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});

export const pollsTable = pgTable("poll_table", {
  id: uuid("id")
    .primaryKey()
    .references(() => feedItemTable.id),
  title: varchar("title", { length: 255 }).notNull(),
  multiSelect: boolean("multi_select").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => new Date())
});

export const pollOptionsTable = pgTable("poll_options_table", {
  id: uuid("id").primaryKey().defaultRandom(),
  pollId: uuid("poll_id")
    .notNull()
    .references(() => pollsTable.id, { onDelete: "cascade" }),
  order: integer("order").notNull().default(1),
  title: varchar("title", { length: 255 }).notNull()
});

export const pollVotesTable = pgTable("poll_votes", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  pollId: uuid("poll_id").notNull(),
  pollOptionId: uuid("poll_option_id")
    .notNull()
    .references(() => pollOptionsTable.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow(),
  deletedAt: timestamp("deleted_at")
});

export const likesTable = pgTable("likes", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  feedItemId: uuid("feed_item_id")
    .notNull()
    .references(() => feedItemTable.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow(),
  deletedAt: timestamp("deleted_at")
});
