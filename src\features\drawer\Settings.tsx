import { Text, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import { Switch } from '@components/shared/animated';
import { router } from '@navigation/refs/navigation-ref';
import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { ScrollView } from 'react-native-gesture-handler';
import { DrawerItem } from './components/DrawerItem';

export const SettingsScreen = () => {
  const { control } = useForm({
    defaultValues: {
      notifications: true,
      privateProfile: false,
    },
  });
  return (
    <ScreenWrapper
      title={
        <Text ff="PlayfairDisplay-SemiBold" fs="20" fw="600">
          Settings
        </Text>
      }>
      <View display="flex" style={{ height: '100%' }} flex={1}>
        <ScrollView bounces={false} contentContainerStyle={{ paddingTop: 16, paddingBottom: 16 }}>
          <DrawerItem showChevronRight onPress={() => router.navigate('BlockedAccounts')} title="Blocked accounts" />
          <DrawerItem
            component={
              <Controller
                control={control}
                name="notifications"
                render={({ field: { value, onChange } }) => (
                  <Switch
                    isActive={value}
                    onToggle={newValue => {
                      onChange(newValue);
                    }}
                  />
                )}
              />
            }
            title="Notifications"
          />
          <DrawerItem
            component={
              <Controller
                control={control}
                name="privateProfile"
                render={({ field: { value, onChange } }) => (
                  <Switch
                    isActive={value}
                    onToggle={newValue => {
                      onChange(newValue);
                    }}
                  />
                )}
              />
            }
            title="Private profile"
          />
          <DrawerItem onPress={() => {}} title="Deactivate account" />
          <DrawerItem redTitle onPress={() => {}} title="Delete account" />
        </ScrollView>
      </View>
    </ScreenWrapper>
  );
};
