import { Text, View } from '@components/native';
import { Ripple } from '@components/shared/animated';
import { useTheme } from '@context/index';
import { useState } from 'react';
import Animated, { useAnimatedStyle, withTiming } from 'react-native-reanimated';
import Feather from 'react-native-vector-icons/Feather';

type DrawerItemProps = {
  onPress?: () => void;
  icon?: React.ReactElement;
  title: string;
  noBorder?: boolean;
  showChevronRight?: boolean;
  component?: React.ReactElement;
  redTitle?: boolean;
  dropDownText?: string;
};

export const DrawerItem: React.FC<DrawerItemProps> = ({ onPress, title, icon, noBorder = false, showChevronRight = false, component, redTitle = false, dropDownText }) => {
  const { colors } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  const rotateStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: withTiming(`${isOpen ? 90 : 0}deg`, { duration: 300 }) }],
    };
  });

  const heightStyle = useAnimatedStyle(() => {
    return {
      maxHeight: withTiming(isOpen ? 500 : 0, { duration: 300 }),
      opacity: withTiming(isOpen ? 1 : 0, { duration: 300 }),
      overflow: 'hidden',
    };
  });

  const handlePress = () => {
    if (dropDownText) {
      setIsOpen(!isOpen);
    }
    onPress?.();
  };

  return (
    <>
      <Ripple onPress={handlePress} style={{ paddingVertical: 16, paddingHorizontal: 24, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
        <View ai="center" display="flex" fd="row" jc="space-between">
          <View display="flex" fd="row" ai="center" gap={icon ? 16 : 0}>
            <View miw={16}>{icon}</View>
            <Text color={redTitle ? 'negative50' : 'neutral70'} fw="500" fs="14">
              {title}
            </Text>
          </View>
          {component}
          {showChevronRight && (
            <Animated.View style={dropDownText ? rotateStyle : undefined}>
              <Feather name="chevron-right" size={20} color={colors.neutral70} />
            </Animated.View>
          )}
        </View>
      </Ripple>
      {dropDownText && (
        <Animated.View style={heightStyle}>
          <View px={24} py={5}>
            <Text color="neutral60" fs="12">
              {dropDownText}
            </Text>
          </View>
        </Animated.View>
      )}
      {!noBorder && <View mx={24} h={0.5} bg="neutral00" />}
    </>
  );
};
