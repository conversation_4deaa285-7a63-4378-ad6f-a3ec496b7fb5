import { ThemeColorKeys } from '@/types/color-types';
import { Text, View } from '@components/native';
import { useTheme } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { BounceTap } from './animated';

export type StackBackButtonProps = {
  bg?: ThemeColorKeys;
  title?: string;

  /**
   * Default true
   */
  withAbsolute?: boolean;
  onPress?: () => void;
  hideBackArrow?: boolean;
  bc?: ThemeColorKeys;
};

export const StackBackButton: React.FC<StackBackButtonProps> = ({
  bg = 'transparent',
  title,
  bc = 'purpleLight',
  hideBackArrow = false,
  withAbsolute = true,
  onPress = () => router.back(),
}) => {
  const { colors } = useTheme();
  const { top } = useSafeAreaInsets();

  let baseComponent = (
    <View {...(title && { display: 'flex', fd: 'row', ai: 'center' })} gap={0}>
      {hideBackArrow ? (
        <View h={50} />
      ) : (
        <BounceTap onPress={onPress}>
          <View h={50} bg={bg} bc={bc} bw={1} flexCenterRow style={{ height: 50, width: 50 }} br={100}>
            <Ionicons name="arrow-back-outline" color={colors.neutral70} size={22} />
          </View>
        </BounceTap>
      )}
      {title && (
        <Text ml={15} fw="600" color="neutral80" ta="center" fs="18">
          {title}
        </Text>
      )}
    </View>
  );

  if (withAbsolute) {
    baseComponent = (
      <View top={top + 10} left={15} z={100} pos="absolute">
        {baseComponent}
      </View>
    );
  }

  return <>{baseComponent}</>;
};
