import { Infer } from "@vinejs/vine/types";
import type { Request, Response, NextFunction } from "express";
import { journalValidator, updateJournalValidator } from "./validator.js";
import { mediaBodyValidator } from "@/lib/validation-module.js";
import db from "@repo/db";
import { MediaModule } from "@repo/bin-packages";
import {
  filesTable,
  journalCategoryTable,
  JournalChunk,
  journalSearchHistoryTable,
  journalTable,
} from "@repo/db/schema";
import {
  and,
  count,
  desc,
  eq,
  ilike,
  inArray,
  isNull,
  not,
  or,
  sql,
} from "drizzle-orm";
import { Paginator } from "@/lib/pagination-module.js";

export class JournalController {
  @mediaBodyValidator(journalValidator, true)
  static async createJournal(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof journalValidator>;
      const imagesToUpload =
        req.files?.image instanceof Array
          ? req.files.image
          : [req.files?.image];

      const audioToUpload =
        req.files?.audio instanceof Array
          ? req.files.audio
          : [req.files?.audio];

      const journal = await db.transaction(async (tx) => {
        // TODO: upload image to s3
        const images = await MediaModule.uploadJournalImages(
          req.user.id,
          imagesToUpload.filter((f) => f !== undefined),
        );

        const audio = await MediaModule.uploadJournalAudio(
          req.user.id,
          audioToUpload.filter((f) => f !== undefined),
        );

        const parsedJournal: {
          title: string;
          description: JournalChunk[];
          category: string;
        } = {
          title: payload.title,
          description: payload.description.map((d) => {
            if (d.type == "image-file")
              return {
                type: "image-url",
                value: images.find((f) => f.fileName == d.fileName)!.url,
              };
            else if (d.type == "audio-file")
              return {
                type: "audio-url",
                value: audio.find((f) => f.fileName == d.fileName)!.url,
              };
            return d;
          }),
          category: payload.category,
        };

        const [journal] = await tx
          .insert(journalTable)
          .values({
            title: parsedJournal.title,
            description: parsedJournal.description,
            userId: req.user.id,
            summary: payload.summary,
            categoryId: parsedJournal.category,
          })
          .returning();

        return journal;
      });

      res.status(201).json({ message: "created journal", data: journal });
    } catch (error) {
      next(error);
    }
  }

  @mediaBodyValidator(updateJournalValidator, true)
  static async update(req: Request, res: Response, next: NextFunction) {
    try {
      //! Not handling audio file
      const journalId = req.params.id as string;
      const payload = req.body as Infer<typeof updateJournalValidator>;
      const imageFiles =
        req.files?.image instanceof Array
          ? req.files.image
          : [req.files?.image];

      const audioFiles =
        req.files?.audio instanceof Array
          ? req.files.audio
          : [req.files?.audio];

      const imagesToUpload = payload.description
        .filter((d) => d.type == "image-file")
        .map((d) => d.fileName);

      const audioToUpload = payload.description
        .filter((d) => d.type == "audio-file")
        .map((d) => d.fileName);

      const journal = await db.transaction(async (tx) => {
        // TODO: upload image to s3
        const images = await MediaModule.uploadJournalImages(
          req.user.id,
          imageFiles
            .filter((f) => f !== undefined)
            .filter((f) => imagesToUpload.includes(f.name)),
        );

        const audio = await MediaModule.uploadJournalAudio(
          req.user.id,
          audioFiles
            .filter((f) => f !== undefined)
            .filter((f) => audioToUpload.includes(f.name)),
        );

        const parsedJournal: {
          title: string;
          description: JournalChunk[];
          category: string;
        } = {
          title: payload.title,
          description: payload.description.map((d) => {
            if (d.type == "image-file")
              return {
                type: "image-url",
                value: images.find((f) => f.fileName == d.fileName)!.url,
              };
            else if (d.type == "audio-file")
              return {
                type: "audio-url",
                value: audio.find((f) => f.fileName == d.fileName)!.url,
              };
            return d;
          }),
          category: payload.category,
        };

        const [foundJournal] = await tx
          .select({
            id: journalTable.id,
            description: journalTable.description,
          })
          .from(journalTable)
          .where(eq(journalTable.id, journalId))
          .limit(1);

        if (!foundJournal) {
          throw new Error("Journal not found");
        }

        const [journal] = await tx
          .update(journalTable)
          .set({
            title: parsedJournal.title,
            description: parsedJournal.description,
            summary: payload.summary,
            userId: req.user.id,
          })
          .where(eq(journalTable.id, journalId))
          .returning();

        // cleanup
        // TODO: take oldImages from frontend to optimize it
        if (payload.mediaUpdated) {
          const chunks = foundJournal.description.filter(
            (d) => d.type == "image-url" || d.type == "audio-url",
          );

          const toBeDeleted = chunks.filter((f) =>
            payload.description.find(
              (d) =>
                (d.type == "image-url" && d.value != f.value) ||
                (d.type == "image-file" && d.value != f.value),
            ),
          );

          await tx.delete(filesTable).where(
            inArray(
              filesTable.url,
              toBeDeleted.map((f) => f.value),
            ),
          );
        }
        return journal;
      });

      res.status(201).json({ message: "updated journal", data: journal });
    } catch (error) {
      next(error);
    }
  }

  static async delete(req: Request, res: Response, next: NextFunction) {
    try {
      const journalId = req.params.id as string;
      await db
        .update(journalTable)
        .set({ deletedAt: new Date() })
        .where(eq(journalTable.id, journalId));
      res.status(201).json({ message: "deleted journal" });
    } catch (error) {
      next(error);
    }
  }
  static async getJournals(req: Request, res: Response, next: NextFunction) {
    try {
      const { limit, offset, page } = Paginator.getPage(req.query);
      const category = req.query.category as string;
      const q = req.query.q as string | undefined;
      const filter = [
        eq(journalTable.userId, req.user.id),
        isNull(journalTable.deletedAt),
      ];

      if (category) {
        filter.push(eq(journalCategoryTable.id, category));
      }

      if (q) {
        // This becomes the boolean condition
        filter.push(sql`(
        setweight(to_tsvector('english', ${journalTable.title}), 'A') ||
        setweight(to_tsvector('english', ${journalTable.summary}), 'B')
      ) @@ plainto_tsquery('english', ${q})`);
      }

      const total = await Paginator.getTotalCount(
        db
          .select({ count: count() })
          .from(journalTable)
          .innerJoin(
            journalCategoryTable,
            eq(journalTable.categoryId, journalCategoryTable.id),
          )
          .where(and(...filter)),
      );

      const rows = await db
        .select({
          id: journalTable.id,
          title: journalTable.title,
          summary: journalTable.summary,
          updatedAt: journalTable.updatedAt,
          createdAt: journalTable.createdAt,
          rank: q
            ? sql`ts_rank(
              setweight(to_tsvector('english', ${journalTable.title}), 'A') ||
              setweight(to_tsvector('english', ${journalTable.summary}), 'B'),
              plainto_tsquery('english', ${q})
            ) as rank`
            : sql`NULL as rank`,
          category: {
            id: journalCategoryTable.id,
            type: journalCategoryTable.type,
            title: journalCategoryTable.title,
            bgImage: journalCategoryTable.bgImage,
          },
        })
        .from(journalTable)
        .innerJoin(
          journalCategoryTable,
          eq(journalTable.categoryId, journalCategoryTable.id),
        )
        .where(and(...filter))
        .orderBy(desc(q ? sql`rank` : journalTable.createdAt))
        .limit(limit)
        .offset(offset);

      const data = Paginator.paginate(req.query, rows, total);
      res.json({ data });
    } catch (error) {
      next(error);
    }
  }

  static async getJournalById(req: Request, res: Response, next: NextFunction) {
    try {
      const journalId = req.params.id as string;
      const filter = [
        eq(journalTable.userId, req.user.id),
        isNull(journalTable.deletedAt),
        eq(journalTable.id, journalId),
      ];

      const [journal] = await db
        .select({
          id: journalTable.id,
          title: journalTable.title,
          description: journalTable.description,
          summary: journalTable.summary,
          updatedAt: journalTable.updatedAt,
          createdAt: journalTable.createdAt,
          category: {
            id: journalCategoryTable.id,
            type: journalCategoryTable.type,
            title: journalCategoryTable.title,
            bgImage: journalCategoryTable.bgImage,
          },
        })
        .from(journalTable)
        .innerJoin(
          journalCategoryTable,
          eq(journalTable.categoryId, journalCategoryTable.id),
        )
        .where(and(...filter));

      res.json({ data: { data: journal } });
    } catch (error) {
      next(error);
    }
  }

  static async search(req: Request, res: Response, next: NextFunction) {
    try {
      const { limit, offset, page } = Paginator.getPage(req.query);
      const q = req.query.q as string;

      const filter: any[] = [
        eq(journalTable.userId, req.user.id),
        isNull(journalTable.deletedAt),
      ];
      let matchQuery = null;
      if (q) {
        // This becomes the boolean condition
        matchQuery = sql`(
        setweight(to_tsvector('english', ${journalTable.title}), 'A') ||
        setweight(to_tsvector('english', ${journalTable.summary}), 'B')
      ) @@ plainto_tsquery('english', ${q})`;

        filter.push(matchQuery);
      }

      const total = await Paginator.getTotalCount(
        db
          .select({ count: count() })
          .from(journalTable)
          .innerJoin(
            journalCategoryTable,
            eq(journalTable.categoryId, journalCategoryTable.id),
          )
          .where(and(...filter)),
      );

      const journals = await db
        .select({
          id: journalTable.id,
          title: journalTable.title,
          summary: journalTable.summary,
          updatedAt: journalTable.updatedAt,
          createdAt: journalTable.createdAt,
          category: {
            id: journalCategoryTable.id,
            type: journalCategoryTable.type,
            title: journalCategoryTable.title,
            bgImage: journalCategoryTable.bgImage,
          },
          rank: q
            ? sql`ts_rank(
              setweight(to_tsvector('english', ${journalTable.title}), 'A') ||
              setweight(to_tsvector('english', ${journalTable.summary}), 'B'),
              plainto_tsquery('english', ${q})
            )`
            : sql`NULL`,
        })
        .from(journalTable)
        .innerJoin(
          journalCategoryTable,
          eq(journalTable.categoryId, journalCategoryTable.id),
        )
        .where(and(...filter))
        .orderBy(
          q
            ? sql`ts_rank(
              setweight(to_tsvector('english', ${journalTable.title}), 'A') ||
              setweight(to_tsvector('english', ${journalTable.summary}), 'B'),
              plainto_tsquery('english', ${q})
            ) DESC`
            : desc(journalTable.createdAt),
        )
        .limit(limit)
        .offset(offset);

      if (q) {
        await db.insert(journalSearchHistoryTable).values({
          query: q,
          userId: req.user.id,
          journalId: journals[0]?.id,
        });
      }

      const data = Paginator.paginate(req.query, journals, total);

      res.json({ data: data });
    } catch (error) {
      next(error);
    }
  }

  static async getSearchHistory(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const limit = 10;
      const rows = await db
        .select({
          id: journalSearchHistoryTable.id,
          text: journalSearchHistoryTable.query,
          timestamp: journalSearchHistoryTable.createdAt,
        })
        .from(journalSearchHistoryTable)
        .where(eq(journalSearchHistoryTable.userId, req.user.id))
        .orderBy(desc(journalSearchHistoryTable.createdAt))
        .limit(limit);

      res.json({ data: { data: rows } });
    } catch (error) {
      next(error);
    }
  }
}
