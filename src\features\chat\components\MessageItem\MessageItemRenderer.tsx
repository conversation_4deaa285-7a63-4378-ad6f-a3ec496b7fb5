import { Text } from '@components/native';
import { ChatMessage } from '@packages/useStitch/types';
import { TextMessageBubble } from './TextMessageBubble';

type MessageItemRenderer = {
  myUserId: string;
  item: ChatMessage;
};

export const MessageItemRenderer: React.FC<MessageItemRenderer> = ({ item, myUserId }) => {
  if (!item.type) {
    console.warn('Invalid message type:', item);
    return <Text>Invalid Message Type</Text>;
  }
  switch (item.type) {
    case 'audio':
      return <Text>Audio Item</Text>;

    case 'text':
      return <TextMessageBubble item={item} myUserId={myUserId} />;
  }
};
