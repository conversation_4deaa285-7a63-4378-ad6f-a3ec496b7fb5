export enum CHAT_STATUS {
  pending,
  accepted,
  rejected,
}

export type BestStitcherItem = {
  id: string;
  username: string;
  totalHealCount: string;
  avatar: string;
  chatStatus: CHAT_STATUS;
};

export type BestRecommenedStitcherResponse = BestStitcherItem[];

export type ChatRequestsItem = {
  id: string;
  status: CHAT_STATUS;
  isMutualInterest: boolean;
  user: {
    id: string;
    avatar: string;
    username: string;
  };
};

export type ConversationItem = {
  id: string;
  roomId: string;
  userId: string;
  username: string;
  avatar: string;
  blockedBy: string | null;
  isPinned: boolean;
};

export type ChatLinkPreview = {
  link: string;
  title?: string;
  description?: string;
  imageUrl?: string;
};

export interface ChatMessage {
  id: string;
  message: string;
  type: 'audio' | 'text';
  senderId: string;
  sentAt: string;
  deliveredAt?: string;
  seenAt?: string;
  linkPreview?: ChatLinkPreview;
}

export interface ChatPinUnpinMutationBody {
  pin: boolean;
}
