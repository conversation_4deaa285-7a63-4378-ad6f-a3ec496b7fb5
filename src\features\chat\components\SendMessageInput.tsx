import MicIcon from '@assets/svgs/mic-icon.svg';
import SendMessageIcon from '@assets/svgs/send-message-icon.svg';
import { View } from '@components/native';
import { BounceTap } from '@components/shared/animated';
import { useAuth, useTheme } from '@context/index';
import { MotiView } from 'moti';
import { memo } from 'react';
import { StyleSheet, TextInput } from 'react-native';
import { Easing } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useMessageStore } from '../store/useMessageStore';

export const SendMessageInput = memo(() => {
  const { session } = useAuth();
  const { onSendMessageClick, messageInputValue, setMessageInputValue } = useMessageStore();
  const { bottom } = useSafeAreaInsets();
  const { colors } = useTheme();

  const paddingBottom = bottom > 15 ? bottom : 15;

  const handleSendMessage = () => {
    onSendMessageClick(messageInputValue.trim(), session!.user!.id);
  };

  const isEnabled = messageInputValue.trim().length > 0;

  return (
    <View pb={paddingBottom} bg="background" display="flex" gap={8} ai="center" fd="row" px={20}>
      <BounceTap onPress={() => {}}>
        <View bc="purpleLight" flexCenterRow bw={1} bg="background" br={16} size={54} shadow="md" disableSizeMatter>
          <MicIcon stroke={colors.neutral80} />
        </View>
      </BounceTap>
      <View mah={200} shadow="md" display="flex" fd="row" ai="center" bg="background" bc="purpleLight" bw={1} br={16} flex={1}>
        <TextInput
          maxLength={2000}
          value={messageInputValue}
          onChangeText={setMessageInputValue}
          multiline
          style={styles.chatTextInput}
          placeholder="Type a message..."
          placeholderTextColor={colors.neutral60}
        />
        <BounceTap disabled={!isEnabled} onPress={handleSendMessage}>
          <View bg={isEnabled ? 'purple100' : 'neutral00'} mr={10} p={6} flexCenterRow br={10}>
            <MotiView
              animate={{
                rotate: isEnabled ? '-35deg' : '0deg',
                backgroundColor: isEnabled ? colors.purple100 : colors.negative00,
              }}
              transition={{
                type: 'timing',
                duration: 300,
                easing: Easing.out(Easing.ease),
              }}>
              <SendMessageIcon width={23} height={23} />
            </MotiView>
          </View>
        </BounceTap>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  chatTextInput: {
    paddingHorizontal: 15,
    textAlignVertical: 'center',
    paddingVertical: 17,
    flex: 1,
  },
});
