import { bodyValidator, mediaBodyValidator } from "@/lib/validation-module.js";
import db from "@repo/db";
import {
  journalCategoryTable,
  journalSearchHistoryTable,
  journalTable,
} from "@repo/db/schema";
import { and, count, eq, isNull, or, sql } from "drizzle-orm";
import type { Request, Response, NextFunction } from "express";
import { createJournalCategoryValidator } from "./validator.js";
import { MediaModule } from "@repo/bin-packages";
import { JournalRepo } from "@repo/repo";
import { Infer } from "@vinejs/vine/types";

export class JournalCategoriesController {
  static async getAllCategories(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const q = req.query.q as string | undefined;
      const filter = [
        or(
          isNull(journalCategoryTable.userId),
          eq(journalCategoryTable.userId, req.user.id),
        ),
      ];

      const join = [
        eq(journalTable.categoryId, journalCategoryTable.id),
        eq(journalTable.userId, req.user.id),
        isNull(journalTable.deletedAt),
      ];

      if (q) {
        // This becomes the boolean condition
        join.push(sql`(
        setweight(to_tsvector('english', ${journalTable.title}), 'A') ||
        setweight(to_tsvector('english', ${journalTable.summary}), 'B')
      ) @@ plainto_tsquery('english', ${q})`);
      }

      const rows = await db
        .select({
          id: journalCategoryTable.id,
          title: journalCategoryTable.title,
          type: journalCategoryTable.type,
          bgImage: journalCategoryTable.bgImage,
          totalCount: count(journalTable.id),
        })
        .from(journalCategoryTable)
        .leftJoin(journalTable, and(...join))
        .where(and(...filter))
        .groupBy(journalCategoryTable.id)
        .orderBy(sql`CASE WHEN "type" = 'all' THEN 1 ELSE 2 END`);

      if (q) {
        await db
          .insert(journalSearchHistoryTable)
          .values({
            query: q,
            userId: req.user.id,
          })
          .onConflictDoUpdate({
            target: [
              journalSearchHistoryTable.query,
              journalSearchHistoryTable.userId,
            ],
            set: {
              journalId: rows[0]?.id,
              createdAt: new Date(),
            },
          });
      }

      const totalJournals = rows.reduce((acc, row) => {
        return acc + row.totalCount;
      }, 0);
      res.json({
        data: {
          data: rows.map((row) => ({
            ...row,
            totalCount: row.type == "all" ? totalJournals : row.totalCount,
          })),
        },
      });
    } catch (error) {
      next(error);
    }
  }
  @bodyValidator(createJournalCategoryValidator)
  static async addCategory(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof createJournalCategoryValidator>;

      const file = await JournalRepo.findCover(payload.coverId);

      if (!file) {
        throw new Error("Cover not found");
      }
      const [category] = await db
        .insert(journalCategoryTable)
        .values({
          userId: req.user.id,
          title: payload.title,
          type: payload.type,
          bgImage: file.url,
        })
        .returning();
      res.json({
        data: { data: category },
        message: "Category created successfully",
      });
    } catch (error) {
      next(error);
    }
  }
  static async getCovers(req: Request, res: Response, next: NextFunction) {
    try {
      const covers = await JournalRepo.listJournalCovers();
      res.json({ data: { data: covers } });
    } catch (error) {
      next(error);
    }
  }
}
