import { View } from '@components/native';
import React from 'react';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import Svg, { Path } from 'react-native-svg';

interface AnimatedMoodFaceProps {
  selectedIndex: number;
  width?: number;
  height?: number;
}

const HeartbrokenFace = (props: { width?: number; height?: number }) => (
  <Svg width={props.width || 230} height={props.height || 223} viewBox="0 0 220 253" fill="none">
    <Path d="M106.5 69.5L100 49L109.5 56.5L114 52L119 72L107.5 92.5L106.5 121.5L98 92.5L106.5 69.5Z" fill="#F3ECEE" />
    <Path d="M124.284 164C118.742 160.8 105.125 156.864 94.9996 166.722" stroke="white" strokeWidth={4} />
    <Path d="M148 106.447C148.748 109.651 150.155 117.114 149.794 121.34" stroke="white" strokeWidth={4} />
    <Path d="M68.4999 106C69.365 109.502 71.0081 117.656 70.6602 122.258" stroke="white" strokeWidth={4} />
  </Svg>
);

const FrustratedFace = (props: { width?: number; height?: number }) => (
  <Svg width={props.width || 230} height={props.height || 223} viewBox="0 0 220 253" fill="none">
    <Path d="M83 87H111" stroke="#646464" strokeWidth={4} />
    <Path d="M154 87L181 79" stroke="#646464" strokeWidth={4} />
    <Path d="M119 139C125.19 132.208 139.857 122.7 149 139" stroke="#646464" strokeWidth={4} />
    <Path d="M11 52.6858C15.9918 57.2358 22.9804 68.0159 11 74.7359" stroke="#E35B5B" strokeWidth={3} strokeLinecap="round" />
    <Path d="M41.522 75.7747C36.5302 71.2246 29.5416 60.4446 41.522 53.7245" stroke="#E35B5B" strokeWidth={3} strokeLinecap="round" />
    <Path d="M15.2361 78.9717C19.7861 73.9799 30.5662 66.9913 37.2862 78.9717" stroke="#E35B5B" strokeWidth={3} strokeLinecap="round" />
    <Path d="M38.406 49C33.856 53.9918 23.0759 60.9804 16.3559 49" stroke="#E35B5B" strokeWidth={3} strokeLinecap="round" />
  </Svg>
);

const AnxiousFace = (props: { width?: number; height?: number }) => (
  <Svg width={props.width || 230} height={props.height || 223} viewBox="0 0 220 253" fill="none">
    <Path d="M59 122L82 111" stroke="white" strokeWidth={4} />
    <Path d="M145 111.268L168.8 126.319" stroke="white" strokeWidth={4} />
    <Path d="M97 168C103.19 161.208 117.857 151.7 127 168" stroke="white" strokeWidth={4} />
    <Path
      d="M178.294 75.4886C180.671 75.4886 182.598 73.92 182.598 71.9851C182.598 70.0502 180.671 68.4817 178.294 68.4817C175.917 68.4817 173.99 70.0502 173.99 71.9851C173.99 73.92 175.917 75.4886 178.294 75.4886Z"
      fill="#FFEEBF"
    />
    <Path
      d="M171.274 76.1015C172.77 76.1015 173.984 77.039 173.984 78.1958C173.984 79.3565 172.77 80.2911 171.274 80.2911C169.776 80.2911 168.56 79.3565 168.56 78.1958C168.56 77.0418 169.776 76.1015 171.274 76.1015ZM180.739 41.7983C189.429 41.7983 196.476 46.933 196.476 53.2658C196.476 59.594 189.429 64.7296 180.739 64.7296C172.046 64.7296 165 59.594 165 53.2658C165 46.9321 172.045 41.7983 180.739 41.7983Z"
      fill="#FFEEBF"
    />
    <Path
      d="M197.628 29.8228C209.804 29.8228 219.678 35.6953 219.678 42.9412C219.678 50.1871 209.805 56.0578 197.628 56.0578C185.447 56.0578 175.577 50.1862 175.577 42.9412C175.577 35.6953 185.447 29.8228 197.628 29.8228Z"
      fill="#FFEEBF"
    />
    <Path
      d="M197.628 32.8499C206.956 32.8499 214.513 37.003 214.513 42.1245C214.513 47.2452 206.956 51.4011 197.628 51.4011C188.298 51.4011 180.735 47.2452 180.735 42.1245C180.735 37.003 188.291 32.8499 197.628 32.8499Z"
      fill="#FFEEBF"
    />
    <Path
      d="M196.475 48.6189C205.802 48.6189 213.368 52.772 213.368 57.8955C213.368 63.0142 205.803 67.1711 196.475 67.1711C187.145 67.1711 179.581 63.0142 179.581 57.8955C179.581 52.772 187.137 48.6189 196.475 48.6189Z"
      fill="#FFEEBF"
    />
    <Path
      d="M208.73 46.9631C217.582 46.9631 224.762 50.9194 224.762 55.7953C224.762 60.675 217.581 64.6313 208.73 64.6313C199.877 64.6313 192.699 60.675 192.699 55.7953C192.699 50.9194 199.876 46.9631 208.73 46.9631Z"
      fill="#FFEEBF"
    />
    <Path
      d="M189.069 47.0878C198.408 50.1126 214.971 54.3475 206.513 47.0878C198.055 39.8281 192.417 41.9887 190.655 43.9765C189.333 46.8285 189.016 52.9993 198.32 54.866C209.949 57.1995 192.769 38.5317 187.747 48.3842C182.726 58.2366 188.049 57.9773 194.884 54.866C201.718 51.7547 205.985 47.0878 198.32 47.0878C190.655 47.0878 183.254 47.6161 189.069 50.7176C194.884 53.8192 204.928 62.6443 205.72 54.866C206.513 47.0878 206.505 34.1185 201.756 40.6059C197.007 47.0934 196.469 48.1249 194.884 52.2733C194.845 52.374 194.795 52.464 194.735 52.5439M194.735 52.5439C192.304 55.7557 172.732 42.5519 185.369 43.9765C198.32 45.4364 203.606 48.9027 201.756 53.8289C199.906 58.7551 193.529 64.4819 194.735 52.5439Z"
      stroke="#585858"
      strokeWidth={1.15}
    />
  </Svg>
);

const OptimisticFace = (props: { width?: number; height?: number }) => (
  <Svg width={props.width || 230} height={props.height || 223} viewBox="0 0 220 253" fill="none">
    <Path d="M50 89C54.6667 96.9789 66.8 108.431 78 90.408" stroke="#808080" strokeWidth={4} />
    <Path d="M96.2012 91.8003C99.9934 102.753 112.147 119.823 130.422 100.479" stroke="#808080" strokeWidth={4} />
    <Path d="M67 132C72.5 138.236 86.25 143.795 100 136.631" stroke="#808080" strokeWidth={4} />
    <Path
      d="M50.6111 142.194C50.4141 140.854 49.8497 139.594 48.9806 138.554C48.1115 137.515 46.9715 136.736 45.687 136.305L29.8111 130.13L34.1445 119.123C34.3411 118.678 34.434 118.194 34.4163 117.708C34.3987 117.221 34.2709 116.745 34.0425 116.315C33.8142 115.885 33.4912 115.513 33.0981 115.226C32.7049 114.939 32.2517 114.745 31.7727 114.659C31.1064 114.537 30.4204 114.578 29.7736 114.779C29.1268 114.981 28.5385 115.336 28.059 115.814C26.5511 117.548 25.1508 119.373 23.8658 121.278C22.5268 120.077 18.2397 116.685 14.2964 118.611C10.1681 120.627 17.425 127.615 19.3779 129.395C15.7191 137.771 15.1818 145.548 16.3215 150.95C17.477 156.439 19.6134 157.884 24.5331 160.796C30.0453 164.122 36.4075 165.77 42.8415 165.538C44.9151 165.495 46.8985 164.681 48.4053 163.256C49.912 161.83 50.8343 159.895 50.9925 157.827C51.3969 152.617 51.2684 147.378 50.6111 142.194Z"
      fill="#FFDC5D"
    />
    <Path
      d="M44.9778 136.026C44.9479 136.146 44.9333 136.269 44.9345 136.392C45.0939 140.44 44.8982 144.495 44.3495 148.509C44.3164 148.886 44.4323 149.26 44.6723 149.552C44.9123 149.845 45.2572 150.031 45.6331 150.072C46.0089 150.112 46.3857 150.004 46.6826 149.77C46.9795 149.536 47.1728 149.195 47.221 148.82C47.6948 145.083 47.9173 141.319 47.8855 137.553C47.2192 137.034 46.4818 136.613 45.6957 136.304L44.9778 136.026ZM37.2645 133.545C37.3146 137.911 36.9274 142.271 36.1089 146.561C36.0659 146.748 36.0608 146.943 36.094 147.133C36.1272 147.322 36.198 147.504 36.3022 147.665C36.4065 147.827 36.5421 147.967 36.701 148.076C36.86 148.184 37.0391 148.26 37.2278 148.299C37.4166 148.337 37.6111 148.338 37.7999 148.3C37.9888 148.262 38.1682 148.187 38.3276 148.079C38.4871 147.971 38.6232 147.832 38.7281 147.67C38.8331 147.509 38.9046 147.328 38.9386 147.138C39.7631 142.857 40.1733 138.507 40.1635 134.147L37.3483 133.067C37.2936 133.22 37.2652 133.382 37.2645 133.545ZM27.8597 146.356H27.9334C28.3047 146.356 28.662 146.213 28.931 145.957C29.2 145.701 29.36 145.351 29.3778 144.98C29.4156 140.21 30.487 135.505 32.518 131.189L29.8112 130.13C27.6758 134.745 26.5435 139.761 26.4889 144.846C26.4713 145.228 26.6056 145.601 26.8625 145.884C27.1194 146.167 27.478 146.336 27.8597 146.356ZM21.5345 119.551C19.8682 121.984 18.4039 124.55 17.1564 127.222C17.8693 127.974 18.6098 128.699 19.3765 129.396C20.6175 126.566 22.1154 123.856 23.8514 121.299C23.134 120.647 22.3585 120.062 21.5345 119.551Z"
      fill="#EF9645"
    />
  </Svg>
);

const HealingFace = (props: { width?: number; height?: number }) => (
  <Svg width={props.width || 230} height={props.height || 223} viewBox="0 0 220 253" fill="none">
    <Path d="M107.653 133.958C112.004 143.157 123.12 156.368 132.77 135.615" stroke="#4B4B4B" strokeWidth={4} />
    <Path
      d="M104.949 97.207C105.725 78.7759 97.5075 89.5274 93.3016 97.207C84.5664 78.776 77.7723 91.6776 80.684 99.9716C83.0134 106.607 90.0663 110.109 93.3016 111.03C101.843 107.344 104.625 100.279 104.949 97.207Z"
      fill="#EF6F6F"
    />
    <Path
      d="M162.004 98.0604C163.675 86.5207 156.883 92.8686 153.278 97.4851C153.101 81.5719 142.004 86.5315 142.286 95.4214C142.512 102.533 145.7 108.089 147.266 109.977C158.796 106.72 161.343 100.528 162.004 98.0604Z"
      fill="#EF6F6F"
    />
    <Path
      d="M27.8725 77.5481L46.9369 58.4426C49.1473 56.2784 52.1221 55.0732 55.2156 55.0885C58.3091 55.1039 61.2718 56.3386 63.4606 58.5247C65.6494 60.7109 66.8878 63.672 66.907 66.7655C66.9262 69.859 65.7247 72.8352 63.5631 75.0483L59.2245 79.5398L44.5193 94.2449L40.0455 98.5689C37.8324 100.73 34.8561 101.932 31.7627 101.913C28.6692 101.894 25.708 100.655 23.5219 98.4663C21.3358 96.2775 20.1011 93.3149 20.0857 90.2214C20.0703 87.1278 21.2756 84.1531 23.4398 81.9426L23.4428 81.9368L27.8637 77.554L27.8666 77.5481H27.8725Z"
      fill="#F6F2FF"
    />
  </Svg>
);

const MOOD_FACES = [HeartbrokenFace, FrustratedFace, AnxiousFace, OptimisticFace, HealingFace];

export const AnimatedMoodFace: React.FC<AnimatedMoodFaceProps> = ({ selectedIndex, width = 241, height = 222 }) => {
  // Crossfade between faces using Animated.View
  return (
    <View style={{ position: 'absolute', width, height }}>
      {MOOD_FACES.map((FaceComponent, idx) => (
        <Animated.View
          key={idx}
          style={{
            position: 'absolute',
            width,
            height,
            opacity: selectedIndex === idx ? 1 : 0,
          }}
          pointerEvents={selectedIndex === idx ? 'auto' : 'none'}>
          <FaceComponent width={width} height={height} />
        </Animated.View>
      ))}
    </View>
  );
};
