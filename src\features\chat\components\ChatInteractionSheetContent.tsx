import { Button, Image, Text, View } from '@components/native';
import { Avatar } from '@components/shared';
import useStitch from '@packages/useStitch';
import { useState } from 'react';
import { Pressable } from 'react-native';

type ChatInteractionSheetContent = {
  userId: string;
  username: string;
  closeSheet: () => void;
};

export const ChatInteractionSheetContent: React.FC<ChatInteractionSheetContent> = ({ userId, username, closeSheet }) => {
  const { data, isError, isLoading } = useStitch('badgesList');
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);

  return (
    <View display="flex" fd="column" ai="center" pb={40} px={20} pt={16}>
      <Avatar withBounceTap={false} />
      <Text color="neutral80" fw="500" ta="center" my={10}>
        Has this interaction with <Text fw="700">{username}</Text> helped you?
      </Text>
      <Text fs="12" ta="center" fw="500" color="neutral60" my={20}>
        Give a badge to motivate them to help others too.
      </Text>

      <View mt={20} disableSizeMatter display="flex" gap={20} ai="center" fd="row" jc="space-between">
        {data?.data.map((item, i) => (
          <Pressable style={{ flex: 1 }} onPress={() => setSelectedIndex(i)} key={i}>
            <View flexCenterColumn br={16} bg={selectedIndex == i ? 'purple200' : 'neutral20'} h={80} flex={1}>
              <Image height={50} width={50} objectFit="contain" source={{ uri: item.activeUrl }} />
              <Text mx={8} ta="center" fw={selectedIndex == i ? '600' : '500'} fs="12">
                {item.title}
              </Text>
            </View>
          </Pressable>
        ))}
      </View>
      <View mt={80} display="flex" fd="row" gap={8} ai="center">
        <Button onPress={closeSheet} h={46} bg="background" bc="purple500" bw={1} color="purple500" isHorizontalAligned>
          Cancel
        </Button>
        <Button onPress={() => {}} h={46} isHorizontalAligned>
          Submit
        </Button>
      </View>
    </View>
  );
};
