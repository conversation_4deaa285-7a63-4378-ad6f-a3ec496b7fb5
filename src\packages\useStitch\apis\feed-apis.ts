import { CACHE_APIS } from '../api-base-cache-keys';
import { ApiResponse, MutationApiConfig, PaginatedApiConfig, PaginatedResponse } from '../types/common-api-types';
import {
    BookmarkFeedItemResponse,
    CreatePollMutationBody,
    CreatePostMutationBody,
    FeedItem,
    LikeFeedItemResponse,
    VotePollMutationBody,
    VotePollResponse,
} from '../types/feed-api-types';

const C = CACHE_APIS.FEED;

export namespace FeedApis {
    export const createPost = 'createPost' as const;
    export const createPoll = 'createPoll' as const;
    export const likeFeedItem = 'likeFeedItem' as const;
    export const bookmarkFeedItem = 'bookmarkFeedItem' as const;
    export const votePoll = 'votePoll' as const;
    export const getFeed = 'getFeed' as const;
}

export const feedApiConfig = {
    createPost: {
        path: '/feed/create-post',
        method: 'POST',
        protected: true,
        mutationBody: undefined as unknown as CreatePostMutationBody,
        responseType: undefined as unknown as ApiResponse<FeedItem>,
        baseCacheKey: C.create_post,
        staleTime: 0,
        type: 'mutation' as const,
    } satisfies MutationApiConfig<FeedItem, CreatePostMutationBody>,

    createPoll: {
        path: '/feed/create-poll',
        method: 'POST',
        protected: true,
        mutationBody: undefined as unknown as CreatePollMutationBody,
        responseType: undefined as unknown as ApiResponse<FeedItem>,
        baseCacheKey: C.create_poll,
        staleTime: 0,
        type: 'mutation' as const,
    } satisfies MutationApiConfig<FeedItem, CreatePollMutationBody>,

    likeFeedItem: {
        path: (params: { id: string }) => `/feed/like/${params.id}`,
        method: 'POST',
        protected: true,
        mutationBody: undefined as unknown as undefined,
        responseType: undefined as unknown as ApiResponse<LikeFeedItemResponse>,
        baseCacheKey: C.like_feed_item,
        staleTime: 0,
        type: 'mutation' as const,
    } satisfies MutationApiConfig<LikeFeedItemResponse, undefined>,

    bookmarkFeedItem: {
        path: (params: { id: string }) => `/feed/bookmark/${params.id}`,
        method: 'POST',
        protected: true,
        mutationBody: undefined as unknown as undefined,
        responseType: undefined as unknown as ApiResponse<BookmarkFeedItemResponse>,
        baseCacheKey: C.bookmark_feed_item,
        staleTime: 0,
        type: 'mutation' as const,
    } satisfies MutationApiConfig<BookmarkFeedItemResponse, undefined>,

    votePoll: {
        path: (params: { id: string }) => `/feed/vote/${params.id}`,
        method: 'POST',
        protected: true,
        mutationBody: undefined as unknown as VotePollMutationBody,
        responseType: undefined as unknown as ApiResponse<VotePollResponse>,
        baseCacheKey: C.vote_poll,
        staleTime: 0,
        type: 'mutation' as const,
    } satisfies MutationApiConfig<VotePollResponse, VotePollMutationBody>,

    getFeed: {
        path: '/feed/list',
        method: 'GET',
        protected: true,
        responseType: undefined as unknown as PaginatedResponse<FeedItem[]>,
        baseCacheKey: C.feed_list,
        pageSize: 20,
        staleTime: 5 * 60 * 1000, // 5 minutes
        type: 'paginated' as const,
        isPaginated: true,
        queryParamBuilder: (params: any) => {
            const queryParams: Record<string, string> = {};
            if (params.type) queryParams.type = params.type;
            if (params.userId) queryParams.userId = params.userId;
            return queryParams;
        },
    } satisfies PaginatedApiConfig<FeedItem[]>,
};