import React, { useCallback, useState } from 'react';
import { GestureResponderEvent, LayoutChangeEvent, Pressable, StyleSheet, ViewStyle } from 'react-native';
import Animated, { Easing, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';

interface RippleProps {
  children: React.ReactNode;
  onPress?: () => void;
  onLongPress?: () => void;
  rippleColor?: string;
  rippleOpacity?: number;
  rippleDuration?: number;
  style?: ViewStyle;
  disabled?: boolean;
}

export const Ripple: React.FC<RippleProps> = ({ children, onPress, onLongPress, rippleColor = 'rgba(0, 0, 0, 0.3)', rippleOpacity = 0.3, rippleDuration = 400, style, disabled = false }) => {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });

  const rippleScale = useSharedValue(0);
  const rippleOpacityValue = useSharedValue(0);
  const rippleX = useSharedValue(0);
  const rippleY = useSharedValue(0);

  const onLayout = useCallback((event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setContainerSize({ width, height });
  }, []);

  const rippleAnimatedStyle = useAnimatedStyle(() => {
    const size = Math.sqrt(containerSize.width ** 2 + containerSize.height ** 2);
    return {
      top: rippleY.value - size / 2,
      left: rippleX.value - size / 2,
      width: size,
      height: size,
      transform: [{ scale: rippleScale.value }],
      opacity: rippleOpacityValue.value,
      backgroundColor: rippleColor,
      position: 'absolute',
      borderRadius: size / 2,
    };
  });

  const handlePressIn = (event: GestureResponderEvent) => {
    if (disabled) return;

    // Reset previous animation to start a new ripple
    rippleScale.value = 0;
    rippleOpacityValue.value = rippleOpacity;

    const { locationX, locationY } = event.nativeEvent;
    rippleX.value = locationX;
    rippleY.value = locationY;

    // Start the scale animation
    rippleScale.value = withTiming(2, {
      duration: rippleDuration,
      easing: Easing.out(Easing.ease),
    });
  };

  const handlePressOut = () => {
    if (disabled) return;

    // Fade out the ripple and reset scale after the animation completes
    rippleOpacityValue.value = withTiming(
      0,
      {
        duration: rippleDuration,
        easing: Easing.out(Easing.ease),
      },
      () => {
        rippleScale.value = 0; // Reset scale after fade-out completes
      },
    );
  };

  return (
    <Pressable onPressIn={handlePressIn} onLongPress={onLongPress} onPressOut={handlePressOut} onPress={onPress} onLayout={onLayout} disabled={disabled} style={[styles.container, style]}>
      {children}
      <Animated.View style={[styles.ripple, rippleAnimatedStyle]} pointerEvents="none" />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    position: 'relative',
  },
  ripple: {
    position: 'absolute',
  },
});
