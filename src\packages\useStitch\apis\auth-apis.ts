// packages/useStitch/apis/profile-apis.ts
import { AutoLoginMutationBody, LogoutMutationBody, SessionUserResponse, SignupLoginMutationBody, VerifyOtpMutationBody } from '@packages/useStitch/types';
import { CACHE_APIS } from '../api-base-cache-keys';
import { ApiResponse, MutationApiConfig } from '../types/common-api-types';
const C = CACHE_APIS.AUTH;

export namespace AuthApis {
  export const signup = 'signup' as const;
  export const autoLogin = 'autoLogin' as const;
  export const login = 'login' as const;
  export const logout = 'logout' as const;
  export const verifyOtp = 'verifyOtp' as const;
}

export const authApiConfig = {
  autoLogin: {
    path: '/auth/token',
    method: 'POST',
    protected: true,
    mutationBody: undefined as unknown as AutoLoginMutationBody,
    responseType: undefined as unknown as ApiResponse<SessionUserResponse>,
    baseCacheKey: C.auto_login,
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<SessionUserResponse, AutoLoginMutationBody>,
  signup: {
    path: '/auth/signup',
    method: 'POST',
    protected: false,
    mutationBody: undefined as unknown as SignupLoginMutationBody,
    responseType: undefined as unknown as ApiResponse<any>,
    baseCacheKey: C.signup,
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<any, SignupLoginMutationBody>,
  login: {
    path: '/auth/login',
    method: 'POST',
    protected: false,
    mutationBody: undefined as unknown as SignupLoginMutationBody,
    responseType: undefined as unknown as ApiResponse<any>,
    baseCacheKey: C.login,
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<any, SignupLoginMutationBody>,
  verifyOtp: {
    path: '/auth/verify',
    method: 'POST',
    protected: true,
    mutationBody: undefined as unknown as VerifyOtpMutationBody,
    responseType: undefined as unknown as ApiResponse<SessionUserResponse>,
    baseCacheKey: C.verify_otp,
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<SessionUserResponse, VerifyOtpMutationBody>,
  logout: {
    path: '/auth/logout',
    method: 'POST',
    protected: true,
    mutationBody: undefined as unknown as LogoutMutationBody,
    responseType: undefined as unknown as ApiResponse<any>,
    baseCacheKey: C.logout,
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<any, LogoutMutationBody>,
};
