import { Text, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import React from 'react';
import { ScrollView } from 'react-native-gesture-handler';
import { DrawerItem } from './components/DrawerItem';

export const FaqsScreen = () => {
  return (
    <ScreenWrapper
      title={
        <Text ff="PlayfairDisplay-SemiBold" fs="20" fw="600">
          FAQs
        </Text>
      }>
      <View display="flex" style={{ height: '100%' }} flex={1}>
        <ScrollView bounces={false} contentContainerStyle={{ paddingTop: 16, paddingBottom: 16 }}>
          <DrawerItem
            showChevronRight
            onPress={() => {}}
            title="1.  What is this app for?"
            dropDownText="This app is for people who are going through a tough time. It is a safe space for you to express yourself and connect with others who are going through similar experiences."
          />
          <DrawerItem
            showChevronRight
            onPress={() => {}}
            title="2.  Who can I connect with here?"
            dropDownText="You can connect with other users who are going through similar experiences."
          />
          <DrawerItem showChevronRight onPress={() => {}} title="3.  Is my journal private?" dropDownText="Yes, your journal is private. Only you can see your journal." />
          <DrawerItem
            showChevronRight
            onPress={() => {}}
            title="4.  Can I stay anonymous?"
            dropDownText="Yes, you can stay anonymous. We do not ask for your name or any personal information."
          />
          <DrawerItem showChevronRight onPress={() => {}} title="5.  How does matching work?" />
          <DrawerItem
            showChevronRight
            onPress={() => {}}
            title="6.  Is this a dating app?"
            dropDownText="No, this is not a dating app. We are not looking for romantic relationships. We are looking for people who are going through similar experiences."
          />
          <DrawerItem showChevronRight onPress={() => {}} title="7.  Can I talk to a counselor here?" />
          <DrawerItem showChevronRight onPress={() => {}} title="8.  Is the app free?" dropDownText="Yes, the app is free to use." />
          <DrawerItem
            showChevronRight
            onPress={() => {}}
            title="9.  What kind of loss is this app for?"
            dropDownText="This app is for people who are going through a tough time. It is a safe space for you to express yourself and connect with others who are going through similar experiences."
          />
        </ScrollView>
      </View>
    </ScreenWrapper>
  );
};
